import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/models/api_models.dart';

// Events
abstract class SongSearchEvent extends Equatable {
  const SongSearchEvent();

  @override
  List<Object?> get props => [];
}

class SearchSongsEvent extends SongSearchEvent {
  final String query;
  final String category;

  const SearchSongsEvent({
    required this.query,
    this.category = 'all',
  });

  @override
  List<Object?> get props => [query, category];
}

class LoadSongDetailsEvent extends SongSearchEvent {
  final String songId;

  const LoadSongDetailsEvent(this.songId);

  @override
  List<Object?> get props => [songId];
}

class LoadSongsByThemeEvent extends SongSearchEvent {
  final String theme;

  const LoadSongsByThemeEvent(this.theme);

  @override
  List<Object?> get props => [theme];
}

class ToggleFavoriteEvent extends SongSearchEvent {
  final String songId;

  const ToggleFavoriteEvent(this.songId);

  @override
  List<Object?> get props => [songId];
}

// States
abstract class SongSearchState extends Equatable {
  const SongSearchState();

  @override
  List<Object?> get props => [];
}

class SongSearchInitial extends SongSearchState {}

class SongSearchLoading extends SongSearchState {}

class SongSearchResults extends SongSearchState {
  final List<Song> songs;
  final String query;
  final String category;

  const SongSearchResults({
    required this.songs,
    required this.query,
    required this.category,
  });

  @override
  List<Object?> get props => [songs, query, category];
}

class SongDetailsLoaded extends SongSearchState {
  final Song song;

  const SongDetailsLoaded(this.song);

  @override
  List<Object?> get props => [song];
}

class SongSearchError extends SongSearchState {
  final String message;

  const SongSearchError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class SongSearchBloc extends Bloc<SongSearchEvent, SongSearchState> {
  final ApiService apiService;
  final StorageService storageService;

  SongSearchBloc({
    required this.apiService,
    required this.storageService,
  }) : super(SongSearchInitial()) {
    on<SearchSongsEvent>(_onSearchSongs);
    on<LoadSongDetailsEvent>(_onLoadSongDetails);
    on<LoadSongsByThemeEvent>(_onLoadSongsByTheme);
    on<ToggleFavoriteEvent>(_onToggleFavorite);
  }

  Future<void> _onSearchSongs(SearchSongsEvent event, Emitter<SongSearchState> emit) async {
    emit(SongSearchLoading());
    
    try {
      final songs = await apiService.searchSongs(
        query: event.query,
        category: event.category,
      );

      // Add to recent searches
      await storageService.addRecentSearch(event.query);

      emit(SongSearchResults(
        songs: songs,
        query: event.query,
        category: event.category,
      ));
    } catch (e) {
      emit(SongSearchError('Failed to search songs: $e'));
    }
  }

  Future<void> _onLoadSongDetails(LoadSongDetailsEvent event, Emitter<SongSearchState> emit) async {
    emit(SongSearchLoading());
    
    try {
      final song = await apiService.getSongDetails(songId: event.songId);
      emit(SongDetailsLoaded(song));
    } catch (e) {
      emit(SongSearchError('Failed to load song details: $e'));
    }
  }

  Future<void> _onLoadSongsByTheme(LoadSongsByThemeEvent event, Emitter<SongSearchState> emit) async {
    emit(SongSearchLoading());
    
    try {
      final songs = await apiService.getSongsByTheme(theme: event.theme);
      emit(SongSearchResults(
        songs: songs,
        query: 'Theme: ${event.theme}',
        category: 'theme',
      ));
    } catch (e) {
      emit(SongSearchError('Failed to load songs by theme: $e'));
    }
  }

  Future<void> _onToggleFavorite(ToggleFavoriteEvent event, Emitter<SongSearchState> emit) async {
    try {
      final isFavorite = storageService.isFavoriteSong(event.songId);
      
      if (isFavorite) {
        await storageService.removeFavoriteSong(event.songId);
      } else {
        await storageService.addFavoriteSong(event.songId);
      }
    } catch (e) {
      emit(SongSearchError('Failed to toggle favorite: $e'));
    }
  }
}
