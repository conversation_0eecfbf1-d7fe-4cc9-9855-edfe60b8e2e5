<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Railway Choir API Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #1976D2;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #1976D2;
            padding-bottom: 10px;
        }
        
        .button {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(25, 118, 210, 0.3);
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #1976D2;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .error {
            border-left-color: #f44336;
            background: #ffeaea;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .status {
            text-align: center;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.online {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4CAF50;
        }
        
        .status.offline {
            background: #ffeaea;
            color: #c62828;
            border: 2px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Railway Choir API</h1>
            <p>Test Interface for Mobile App Backend</p>
        </div>
        
        <div class="content">
            <div id="status" class="status offline">
                🔄 Checking API status...
            </div>
            
            <div class="grid">
                <!-- Bible Game Section -->
                <div class="section">
                    <h2>📖 Bible Game</h2>
                    
                    <div class="input-group">
                        <label>User ID:</label>
                        <input type="text" id="userId" value="test_user_web" placeholder="Enter user ID">
                    </div>
                    
                    <div class="input-group">
                        <label>Language:</label>
                        <select id="language">
                            <option value="english">English</option>
                            <option value="malayalam">Malayalam</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label>Difficulty:</label>
                        <select id="difficulty">
                            <option value="Easy">Easy</option>
                            <option value="Medium">Medium</option>
                            <option value="Hard">Hard</option>
                        </select>
                    </div>
                    
                    <button class="button" onclick="startGame()">🎮 Start Game</button>
                    <button class="button" onclick="getLeaderboard()">🏅 Leaderboard</button>
                    
                    <div id="gameResult" class="result" style="display: none;"></div>
                </div>
                
                <!-- Bible Reading Section -->
                <div class="section">
                    <h2>📚 Bible Reading</h2>
                    
                    <div class="input-group">
                        <label>Book:</label>
                        <input type="text" id="book" value="John" placeholder="e.g., John, Genesis">
                    </div>
                    
                    <div class="input-group">
                        <label>Chapter:</label>
                        <input type="number" id="chapter" value="3" placeholder="Chapter number">
                    </div>
                    
                    <div class="input-group">
                        <label>Search Query:</label>
                        <input type="text" id="searchQuery" value="love" placeholder="Search for verses">
                    </div>
                    
                    <button class="button" onclick="getChapter()">📖 Get Chapter</button>
                    <button class="button" onclick="searchBible()">🔍 Search</button>
                    
                    <div id="bibleResult" class="result" style="display: none;"></div>
                </div>
                
                <!-- Song Search Section -->
                <div class="section">
                    <h2>🎵 Song Search</h2>
                    
                    <div class="input-group">
                        <label>Song Query:</label>
                        <input type="text" id="songQuery" value="amazing" placeholder="Search for songs">
                    </div>
                    
                    <div class="input-group">
                        <label>Category:</label>
                        <select id="category">
                            <option value="all">All</option>
                            <option value="hymns">Hymns</option>
                            <option value="lyrics">Lyrics</option>
                            <option value="conventions">Conventions</option>
                        </select>
                    </div>
                    
                    <button class="button" onclick="searchSongs()">🎵 Search Songs</button>
                    <button class="button" onclick="getSongDetails()">📄 Get Song H-1</button>
                    
                    <div id="songResult" class="result" style="display: none;"></div>
                </div>
                
                <!-- API Status Section -->
                <div class="section">
                    <h2>🔧 API Status</h2>
                    
                    <button class="button" onclick="checkHealth()">❤️ Health Check</button>
                    <button class="button" onclick="openDocs()">📚 API Docs</button>
                    <button class="button" onclick="clearResults()">🧹 Clear Results</button>
                    
                    <div id="statusResult" class="result" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        // Check API status on load
        window.onload = function() {
            checkApiStatus();
        };
        
        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const statusDiv = document.getElementById('status');
                
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status online';
                    statusDiv.innerHTML = `✅ API Online - Version ${data.version}`;
                } else {
                    throw new Error('API not responding');
                }
            } catch (error) {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status offline';
                statusDiv.innerHTML = '❌ API Offline - Make sure server is running: py simple_api_server.py';
            }
        }
        
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    data: result,
                    status: response.status
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `Error: ${result.error || result.data?.detail || 'Unknown error'}`;
            }
        }
        
        async function startGame() {
            const data = {
                user_id: document.getElementById('userId').value,
                language: document.getElementById('language').value,
                difficulty: document.getElementById('difficulty').value
            };
            
            const result = await apiCall('/bible-game/start', 'POST', data);
            showResult('gameResult', result);
        }
        
        async function getLeaderboard() {
            const difficulty = document.getElementById('difficulty').value;
            const result = await apiCall(`/bible-game/leaderboard?difficulty=${difficulty}&limit=5`);
            showResult('gameResult', result);
        }
        
        async function getChapter() {
            const book = document.getElementById('book').value;
            const chapter = document.getElementById('chapter').value;
            const language = document.getElementById('language').value;
            
            const result = await apiCall(`/bible/${book}/${chapter}?language=${language}`);
            showResult('bibleResult', result);
        }
        
        async function searchBible() {
            const query = document.getElementById('searchQuery').value;
            const language = document.getElementById('language').value;
            
            const result = await apiCall(`/bible/search?query=${encodeURIComponent(query)}&language=${language}&limit=10`);
            showResult('bibleResult', result);
        }
        
        async function searchSongs() {
            const query = document.getElementById('songQuery').value;
            const category = document.getElementById('category').value;
            
            const result = await apiCall(`/songs/search?query=${encodeURIComponent(query)}&category=${category}&limit=10`);
            showResult('songResult', result);
        }
        
        async function getSongDetails() {
            const result = await apiCall('/songs/H-1');
            showResult('songResult', result);
        }
        
        async function checkHealth() {
            const result = await apiCall('/health');
            showResult('statusResult', result);
            checkApiStatus(); // Update main status
        }
        
        function openDocs() {
            window.open('http://localhost:8000/docs', '_blank');
        }
        
        function clearResults() {
            const results = document.querySelectorAll('.result');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
        }
    </script>
</body>
</html>
