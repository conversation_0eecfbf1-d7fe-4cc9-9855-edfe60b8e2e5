import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/models/api_models.dart';

// Events
abstract class BibleReaderEvent extends Equatable {
  const BibleReaderEvent();

  @override
  List<Object?> get props => [];
}

class LoadChapterEvent extends BibleReaderEvent {
  final String book;
  final int chapter;
  final String language;

  const LoadChapterEvent({
    required this.book,
    required this.chapter,
    required this.language,
  });

  @override
  List<Object?> get props => [book, chapter, language];
}

class SearchBibleEvent extends BibleReaderEvent {
  final String query;
  final String language;

  const SearchBibleEvent({
    required this.query,
    required this.language,
  });

  @override
  List<Object?> get props => [query, language];
}

class AddBookmarkEvent extends BibleReaderEvent {
  final String reference;

  const AddBookmarkEvent(this.reference);

  @override
  List<Object?> get props => [reference];
}

class RemoveBookmarkEvent extends BibleReaderEvent {
  final String reference;

  const RemoveBookmarkEvent(this.reference);

  @override
  List<Object?> get props => [reference];
}

// States
abstract class BibleReaderState extends Equatable {
  const BibleReaderState();

  @override
  List<Object?> get props => [];
}

class BibleReaderInitial extends BibleReaderState {}

class BibleReaderLoading extends BibleReaderState {}

class BibleChapterLoaded extends BibleReaderState {
  final BibleChapter chapter;

  const BibleChapterLoaded(this.chapter);

  @override
  List<Object?> get props => [chapter];
}

class BibleSearchResults extends BibleReaderState {
  final List<BibleSearchResult> results;
  final String query;

  const BibleSearchResults({
    required this.results,
    required this.query,
  });

  @override
  List<Object?> get props => [results, query];
}

class BibleReaderError extends BibleReaderState {
  final String message;

  const BibleReaderError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class BibleReaderBloc extends Bloc<BibleReaderEvent, BibleReaderState> {
  final ApiService apiService;
  final StorageService storageService;

  BibleReaderBloc({
    required this.apiService,
    required this.storageService,
  }) : super(BibleReaderInitial()) {
    on<LoadChapterEvent>(_onLoadChapter);
    on<SearchBibleEvent>(_onSearchBible);
    on<AddBookmarkEvent>(_onAddBookmark);
    on<RemoveBookmarkEvent>(_onRemoveBookmark);
  }

  Future<void> _onLoadChapter(LoadChapterEvent event, Emitter<BibleReaderState> emit) async {
    emit(BibleReaderLoading());
    
    try {
      final chapter = await apiService.getBibleChapter(
        book: event.book,
        chapter: event.chapter,
        language: event.language,
      );

      // Add to reading history
      await storageService.addToReadingHistory(
        event.book,
        event.chapter,
        event.language,
      );

      emit(BibleChapterLoaded(chapter));
    } catch (e) {
      emit(BibleReaderError('Failed to load chapter: $e'));
    }
  }

  Future<void> _onSearchBible(SearchBibleEvent event, Emitter<BibleReaderState> emit) async {
    emit(BibleReaderLoading());
    
    try {
      final results = await apiService.searchBible(
        query: event.query,
        language: event.language,
      );

      emit(BibleSearchResults(
        results: results,
        query: event.query,
      ));
    } catch (e) {
      emit(BibleReaderError('Failed to search: $e'));
    }
  }

  Future<void> _onAddBookmark(AddBookmarkEvent event, Emitter<BibleReaderState> emit) async {
    try {
      await storageService.addBookmark(event.reference);
    } catch (e) {
      emit(BibleReaderError('Failed to add bookmark: $e'));
    }
  }

  Future<void> _onRemoveBookmark(RemoveBookmarkEvent event, Emitter<BibleReaderState> emit) async {
    try {
      await storageService.removeBookmark(event.reference);
    } catch (e) {
      emit(BibleReaderError('Failed to remove bookmark: $e'));
    }
  }
}
