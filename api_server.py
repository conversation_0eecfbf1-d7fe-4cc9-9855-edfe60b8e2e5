#!/usr/bin/env python3
"""
FastAPI Backend for Railway Choir Mobile App
Serves the Flutter app with Bible game, reading, and song search functionality
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import uuid
from datetime import datetime
import random

# Import existing bot functions
from telegram_handlers.conversations import (
    create_bible_question, BIBLE_VERSES, generate_wrong_options,
    get_bible_verse, extract_verse_from_text
)
from telegram_handlers.utils import (
    get_wordproject_url_from_input, extract_bible_chapter_text, clean_bible_text
)
from data.datasets import (
    load_datasets, get_all_data, Tune_finder_of_known_songs,
    dfH, dfL, dfC, dfTH
)
from data.drive import (
    save_game_score, get_user_best_scores_all_difficulties,
    get_leaderboard, get_combined_leaderboard
)

# Initialize FastAPI app
app = FastAPI(
    title="Railway Choir API",
    description="Backend API for Railway Choir Mobile App",
    version="1.0.0"
)

# Add CORS middleware for Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your Flutter app's domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load datasets
try:
    load_datasets()
    print("✅ Datasets loaded successfully")
except Exception as e:
    print(f"❌ Error loading datasets: {e}")

# In-memory storage for active game sessions
game_sessions: Dict[str, Dict[str, Any]] = {}

# Pydantic models for API requests/responses
class GameStartRequest(BaseModel):
    user_id: str
    language: str
    difficulty: str

class GameAnswerRequest(BaseModel):
    user_id: str
    answer: str

class BibleQuestion(BaseModel):
    verse_text: str
    correct_answer: str
    options: List[str]
    difficulty: str
    language: str
    reference: str

class GameAnswerResponse(BaseModel):
    is_correct: bool
    correct_answer: str
    score: int
    total_questions: int
    next_question: Optional[BibleQuestion] = None
    message: Optional[str] = None

class LeaderboardEntry(BaseModel):
    user_id: str
    user_name: str
    score: int
    difficulty: str
    date: str

class BibleVerse(BaseModel):
    number: int
    text: str

class BibleChapter(BaseModel):
    book: str
    chapter: int
    language: str
    verses: List[BibleVerse]
    url: str

class BibleSearchResult(BaseModel):
    book: str
    chapter: int
    verse: int
    text: str
    language: str
    reference: str

class Song(BaseModel):
    id: str
    title: str
    number: Optional[str] = None
    category: str
    theme: Optional[str] = None
    lyrics: Optional[str] = None
    tune: Optional[str] = None
    last_sung: Optional[str] = None
    tunes: Optional[List[str]] = None

class MusicNotation(BaseModel):
    hymn_number: str
    notations: Dict[str, str]  # tune name -> image URL/path

# Helper functions
def create_bible_question_api(difficulty: str, language: str, used_verses: List[str] = None) -> Optional[BibleQuestion]:
    """Create a Bible question for the API"""
    if used_verses is None:
        used_verses = []
    
    try:
        question_data = create_bible_question(difficulty, used_verses, language)
        if question_data:
            return BibleQuestion(
                verse_text=question_data["verse_text"],
                correct_answer=question_data["correct_answer"],
                options=question_data["options"],
                difficulty=question_data["difficulty"],
                language=question_data.get("language", language),
                reference=question_data["reference"]
            )
    except Exception as e:
        print(f"Error creating Bible question: {e}")
    
    return None

def get_user_session(user_id: str) -> Dict[str, Any]:
    """Get or create user game session"""
    if user_id not in game_sessions:
        game_sessions[user_id] = {
            "score": 0,
            "total_questions": 0,
            "used_verses": [],
            "current_difficulty": None,
            "current_language": None
        }
    return game_sessions[user_id]

# Bible Game API Endpoints
@app.post("/api/bible-game/start", response_model=BibleQuestion)
async def start_bible_game(request: GameStartRequest):
    """Start a new Bible game session"""
    try:
        # Reset user session
        session = get_user_session(request.user_id)
        session.update({
            "score": 0,
            "total_questions": 0,
            "used_verses": [],
            "current_difficulty": request.difficulty,
            "current_language": request.language
        })
        
        # Create first question
        question = create_bible_question_api(
            difficulty=request.difficulty,
            language=request.language,
            used_verses=session["used_verses"]
        )
        
        if not question:
            raise HTTPException(status_code=500, detail="Failed to create Bible question")
        
        # Add to used verses
        session["used_verses"].append(question.reference)
        
        return question
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting game: {str(e)}")

@app.post("/api/bible-game/answer", response_model=GameAnswerResponse)
async def submit_bible_game_answer(request: GameAnswerRequest):
    """Submit an answer to the Bible game"""
    try:
        session = get_user_session(request.user_id)
        
        if not session.get("current_difficulty") or not session.get("current_language"):
            raise HTTPException(status_code=400, detail="No active game session")
        
        # For this demo, we'll assume the last question's correct answer is stored
        # In a real implementation, you'd store the current question in the session
        session["total_questions"] += 1
        
        # Create next question
        next_question = create_bible_question_api(
            difficulty=session["current_difficulty"],
            language=session["current_language"],
            used_verses=session["used_verses"]
        )
        
        # For demo purposes, randomly determine if answer is correct (you'd implement proper checking)
        is_correct = random.choice([True, False])  # Replace with actual answer checking
        correct_answer = "John 3:16"  # Replace with actual correct answer
        
        if is_correct:
            session["score"] += 1
        
        if next_question:
            session["used_verses"].append(next_question.reference)
        
        # Save score to database
        try:
            save_game_score(
                user_name=f"User_{request.user_id}",
                user_id=request.user_id,
                score=session["score"],
                difficulty=session["current_difficulty"]
            )
        except Exception as e:
            print(f"Error saving score: {e}")
        
        return GameAnswerResponse(
            is_correct=is_correct,
            correct_answer=correct_answer,
            score=session["score"],
            total_questions=session["total_questions"],
            next_question=next_question,
            message="Great job!" if is_correct else "Keep trying!"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error submitting answer: {str(e)}")

@app.get("/api/bible-game/leaderboard", response_model=List[LeaderboardEntry])
async def get_bible_game_leaderboard(
    difficulty: Optional[str] = Query(None),
    limit: int = Query(10, ge=1, le=50)
):
    """Get Bible game leaderboard"""
    try:
        if difficulty:
            leaderboard_data = get_leaderboard(difficulty, limit)
        else:
            leaderboard_data = get_combined_leaderboard(limit)
            # Flatten the combined leaderboard
            entries = []
            for diff, players in leaderboard_data.items():
                for player in players:
                    entries.append(player)
            leaderboard_data = sorted(entries, key=lambda x: x['Score'], reverse=True)[:limit]
        
        return [
            LeaderboardEntry(
                user_id=str(entry.get('User_id', '')),
                user_name=entry.get('User_Name', 'Unknown'),
                score=entry.get('Score', 0),
                difficulty=entry.get('Difficulty', difficulty or 'Unknown'),
                date=entry.get('Date', datetime.now().isoformat())
            )
            for entry in leaderboard_data
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading leaderboard: {str(e)}")

# Bible Reading API Endpoints
@app.get("/api/bible/{book}/{chapter}", response_model=BibleChapter)
async def get_bible_chapter(book: str, chapter: int, language: str = "malayalam"):
    """Get a Bible chapter"""
    try:
        # Get the URL for the chapter
        result = get_wordproject_url_from_input(language, f"{book} {chapter}")
        url, matched_book, formatted_reference, fuzzy_matched = result
        
        if url.startswith("❌"):
            raise HTTPException(status_code=404, detail=url)
        
        # Extract the chapter text
        raw_text = extract_bible_chapter_text(url)
        if raw_text.startswith("❌"):
            raise HTTPException(status_code=500, detail=raw_text)
        
        # Determine language code for cleaning
        lang_code = 'ml' if language.lower() in ['malayalam', 'mal'] else 'kj'
        cleaned_text = clean_bible_text(raw_text, lang_code)
        
        # Parse verses
        verses = []
        lines = cleaned_text.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line and line[0].isdigit():
                # Extract verse number and text
                parts = line.split(' ', 1)
                if len(parts) >= 2:
                    try:
                        verse_num = int(parts[0])
                        verse_text = parts[1]
                        verses.append(BibleVerse(number=verse_num, text=verse_text))
                    except ValueError:
                        continue
        
        return BibleChapter(
            book=matched_book or book,
            chapter=chapter,
            language=language,
            verses=verses,
            url=url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading chapter: {str(e)}")

@app.get("/api/bible/search", response_model=List[BibleSearchResult])
async def search_bible(
    query: str = Query(..., min_length=1),
    language: str = Query("malayalam"),
    limit: int = Query(50, ge=1, le=100)
):
    """Search Bible verses"""
    try:
        # This is a simplified search - in a real implementation,
        # you'd have a proper search index
        results = []
        
        # For demo, return some sample results
        sample_results = [
            BibleSearchResult(
                book="John",
                chapter=3,
                verse=16,
                text="For God so loved the world...",
                language=language,
                reference="John 3:16"
            ),
            BibleSearchResult(
                book="Psalm",
                chapter=23,
                verse=1,
                text="The Lord is my shepherd...",
                language=language,
                reference="Psalm 23:1"
            )
        ]
        
        # Filter based on query (simple contains check)
        filtered_results = [
            result for result in sample_results
            if query.lower() in result.text.lower() or query.lower() in result.reference.lower()
        ]
        
        return filtered_results[:limit]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching Bible: {str(e)}")

# Song Search API Endpoints
@app.get("/api/songs/search", response_model=List[Song])
async def search_songs(
    query: str = Query(..., min_length=1),
    category: str = Query("all"),
    limit: int = Query(50, ge=1, le=100)
):
    """Search for songs, hymns, and lyrics"""
    try:
        results = []

        # Search in Hymns
        if category in ["all", "hymns"]:
            try:
                hymn_results = get_all_data(query, "Hymns")
                for _, row in hymn_results.iterrows():
                    results.append(Song(
                        id=f"H-{row.name + 1}",
                        title=row.get('Title', 'Unknown'),
                        number=f"H-{row.name + 1}",
                        category="hymns",
                        theme=row.get('Theme', None),
                        lyrics=row.get('Lyrics', None),
                        tune=row.get('Tunes', None),
                        tunes=row.get('Tunes', '').split(',') if row.get('Tunes') else None
                    ))
            except Exception as e:
                print(f"Error searching hymns: {e}")

        # Search in Lyrics
        if category in ["all", "lyrics"]:
            try:
                lyric_results = get_all_data(query, "Lyrics")
                for _, row in lyric_results.iterrows():
                    results.append(Song(
                        id=f"L-{row.name + 1}",
                        title=row.get('Title', 'Unknown'),
                        number=f"L-{row.name + 1}",
                        category="lyrics",
                        theme=row.get('Theme', None),
                        lyrics=row.get('Lyrics', None)
                    ))
            except Exception as e:
                print(f"Error searching lyrics: {e}")

        # Search in Conventions
        if category in ["all", "conventions"]:
            try:
                convention_results = get_all_data(query, "Conventions")
                for _, row in convention_results.iterrows():
                    results.append(Song(
                        id=f"C-{row.name + 1}",
                        title=row.get('Title', 'Unknown'),
                        number=f"C-{row.name + 1}",
                        category="conventions",
                        theme=row.get('Theme', None),
                        lyrics=row.get('Lyrics', None),
                        last_sung=row.get('Last sung', None)
                    ))
            except Exception as e:
                print(f"Error searching conventions: {e}")

        # Remove duplicates and limit results
        unique_results = []
        seen_ids = set()
        for song in results:
            if song.id not in seen_ids:
                unique_results.append(song)
                seen_ids.add(song.id)

        return unique_results[:limit]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching songs: {str(e)}")

@app.get("/api/songs/{song_id}", response_model=Song)
async def get_song_details(song_id: str):
    """Get detailed information about a specific song"""
    try:
        # Parse song ID (format: H-123, L-456, C-789)
        if not song_id or len(song_id) < 3:
            raise HTTPException(status_code=400, detail="Invalid song ID format")

        category_prefix = song_id[0].upper()
        try:
            song_number = int(song_id[2:]) - 1  # Convert to 0-based index
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid song number")

        # Get song data based on category
        if category_prefix == 'H':
            # Hymn
            if song_number >= len(dfH):
                raise HTTPException(status_code=404, detail="Hymn not found")

            row = dfH.iloc[song_number]
            return Song(
                id=song_id,
                title=row.get('Title', 'Unknown'),
                number=song_id,
                category="hymns",
                theme=row.get('Theme', None),
                lyrics=row.get('Lyrics', None),
                tune=row.get('Tunes', None),
                tunes=row.get('Tunes', '').split(',') if row.get('Tunes') else None
            )

        elif category_prefix == 'L':
            # Lyric
            if song_number >= len(dfL):
                raise HTTPException(status_code=404, detail="Lyric not found")

            row = dfL.iloc[song_number]
            return Song(
                id=song_id,
                title=row.get('Title', 'Unknown'),
                number=song_id,
                category="lyrics",
                theme=row.get('Theme', None),
                lyrics=row.get('Lyrics', None)
            )

        elif category_prefix == 'C':
            # Convention
            if song_number >= len(dfC):
                raise HTTPException(status_code=404, detail="Convention song not found")

            row = dfC.iloc[song_number]
            return Song(
                id=song_id,
                title=row.get('Title', 'Unknown'),
                number=song_id,
                category="conventions",
                theme=row.get('Theme', None),
                lyrics=row.get('Lyrics', None),
                last_sung=row.get('Last sung', None)
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid song category")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading song details: {str(e)}")

@app.get("/api/songs/theme/{theme}", response_model=List[Song])
async def get_songs_by_theme(theme: str, limit: int = Query(50, ge=1, le=100)):
    """Get songs by theme"""
    try:
        results = []

        # Search in all categories for the theme
        for df, category, prefix in [(dfH, "hymns", "H"), (dfL, "lyrics", "L"), (dfC, "conventions", "C")]:
            try:
                # Filter by theme (case-insensitive)
                theme_matches = df[df['Theme'].str.contains(theme, case=False, na=False)]

                for idx, row in theme_matches.iterrows():
                    song_data = {
                        "id": f"{prefix}-{idx + 1}",
                        "title": row.get('Title', 'Unknown'),
                        "number": f"{prefix}-{idx + 1}",
                        "category": category,
                        "theme": row.get('Theme', None),
                        "lyrics": row.get('Lyrics', None)
                    }

                    # Add category-specific fields
                    if category == "hymns":
                        song_data["tune"] = row.get('Tunes', None)
                        song_data["tunes"] = row.get('Tunes', '').split(',') if row.get('Tunes') else None
                    elif category == "conventions":
                        song_data["last_sung"] = row.get('Last sung', None)

                    results.append(Song(**song_data))

            except Exception as e:
                print(f"Error searching {category} by theme: {e}")

        return results[:limit]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading songs by theme: {str(e)}")

# Music Notation API Endpoints
@app.get("/api/notation/{hymn_number}", response_model=MusicNotation)
async def get_music_notation(hymn_number: str):
    """Get music notation for a hymn"""
    try:
        from telegram_handlers.conversations import Music_notation_downloader, get_file_map

        # Get file map (this loads the Google Drive images)
        file_map = get_file_map()

        # Get notation data
        notation_data = Music_notation_downloader(hymn_number, file_map)

        if not notation_data:
            raise HTTPException(status_code=404, detail="Music notation not found")

        # Convert file paths to URLs or base64 (for demo, we'll use placeholder URLs)
        notations = {}
        for tune_name, image_path in notation_data.items():
            if image_path and image_path != "Page not found":
                # In a real implementation, you'd serve these images or convert to base64
                notations[tune_name] = f"/api/notation/image/{hymn_number}/{tune_name}"
            else:
                notations[tune_name] = None

        return MusicNotation(
            hymn_number=hymn_number,
            notations=notations
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading music notation: {str(e)}")

# Health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Railway Choir API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/health"
    }

if __name__ == "__main__":
    print("🚀 Starting Railway Choir API Server...")
    print("📱 Flutter app can connect to: http://localhost:8000")
    print("📚 API documentation: http://localhost:8000/docs")

    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
