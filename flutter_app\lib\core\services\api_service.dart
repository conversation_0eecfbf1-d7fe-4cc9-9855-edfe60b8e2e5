import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/api_models.dart';

class ApiService {
  late final Dio _dio;
  // API Configuration
  static const String _developmentUrl = 'http://localhost:8000/api';
  static const String _productionUrl = 'https://your-deployed-api.com/api'; // Update with your deployed URL

  // Automatically detect environment or set manually
  static const bool _isDevelopment = true; // Set to false for production
  static String get baseUrl => _isDevelopment ? _developmentUrl : _productionUrl;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
  }

  // Bible Game API calls
  Future<BibleQuestion> startBibleGame({
    required String userId,
    required String language,
    required String difficulty,
  }) async {
    try {
      final response = await _dio.post('/bible-game/start', data: {
        'user_id': userId,
        'language': language,
        'difficulty': difficulty,
      });

      return BibleQuestion.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<GameAnswerResponse> submitBibleGameAnswer({
    required String userId,
    required String answer,
  }) async {
    try {
      final response = await _dio.post('/bible-game/answer', data: {
        'user_id': userId,
        'answer': answer,
      });

      return GameAnswerResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<LeaderboardEntry>> getBibleGameLeaderboard({
    String? difficulty,
    int limit = 10,
  }) async {
    try {
      final response = await _dio.get('/bible-game/leaderboard', queryParameters: {
        if (difficulty != null) 'difficulty': difficulty,
        'limit': limit,
      });

      return (response.data as List)
          .map((json) => LeaderboardEntry.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Bible Reading API calls
  Future<BibleChapter> getBibleChapter({
    required String book,
    required int chapter,
    String language = 'malayalam',
  }) async {
    try {
      final response = await _dio.get('/bible/$book/$chapter', queryParameters: {
        'language': language,
      });

      return BibleChapter.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<BibleSearchResult>> searchBible({
    required String query,
    String language = 'malayalam',
    int limit = 50,
  }) async {
    try {
      final response = await _dio.get('/bible/search', queryParameters: {
        'query': query,
        'language': language,
        'limit': limit,
      });

      return (response.data as List)
          .map((json) => BibleSearchResult.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Song Search API calls
  Future<List<Song>> searchSongs({
    required String query,
    String category = 'all',
    int limit = 50,
  }) async {
    try {
      final response = await _dio.get('/songs/search', queryParameters: {
        'query': query,
        'category': category,
        'limit': limit,
      });

      return (response.data as List)
          .map((json) => Song.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<Song> getSongDetails({
    required String songId,
  }) async {
    try {
      final response = await _dio.get('/songs/$songId');
      return Song.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Future<List<Song>> getSongsByTheme({
    required String theme,
    int limit = 50,
  }) async {
    try {
      final response = await _dio.get('/songs/theme/$theme', queryParameters: {
        'limit': limit,
      });

      return (response.data as List)
          .map((json) => Song.fromJson(json))
          .toList();
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Music Notation API calls
  Future<MusicNotation> getMusicNotation({
    required String hymnNumber,
  }) async {
    try {
      final response = await _dio.get('/notation/$hymnNumber');
      return MusicNotation.fromJson(response.data);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  // Error handling
  String _handleError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        if (statusCode == 404) {
          return 'Resource not found.';
        } else if (statusCode == 500) {
          return 'Server error. Please try again later.';
        }
        return 'Server error: ${error.response?.statusMessage ?? 'Unknown error'}';
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.unknown:
        return 'Network error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred.';
    }
  }
}
