@echo off
echo 📱 Railway Choir Flutter App Launcher
echo ======================================

echo.
echo 🔍 Checking API server...
py -c "import requests; print('API Status:', requests.get('http://localhost:8000/api/health').status_code)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ API server is not running!
    echo 🚀 Please start it first: py simple_api_server.py
    echo.
    pause
    exit /b 1
)

echo ✅ API server is running

echo.
echo 📂 Navigating to Flutter app...
cd flutter_app

echo.
echo 📦 Getting Flutter dependencies...
C:\flutter\bin\flutter pub get

echo.
echo 🌐 Enabling web support...
C:\flutter\bin\flutter config --enable-web

echo.
echo 🔍 Checking available devices...
C:\flutter\bin\flutter devices

echo.
echo 🚀 Starting Flutter app in Chrome...
echo 💡 This will open the app in your browser
echo 🔗 App will run on: http://localhost:3000
echo 📡 API server: http://localhost:8000
echo.

C:\flutter\bin\flutter run -d chrome --web-port 3000

echo.
echo 🎉 Flutter app session ended
pause
