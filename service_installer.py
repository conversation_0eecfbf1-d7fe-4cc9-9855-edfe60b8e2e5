#!/usr/bin/env python3
"""
Windows Service Installer for Choir Telegram Bot
"""

import sys
import os
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import subprocess
import time
from pathlib import Path

class ChoirBotService(win32serviceutil.ServiceFramework):
    _svc_name_ = "ChoirTelegramBot"
    _svc_display_name_ = "Choir Telegram Bot Service"
    _svc_description_ = "Railway Choir Telegram Bot Background Service"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.is_alive = True
        self.bot_process = None

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_alive = False
        
        # Stop the bot process
        if self.bot_process:
            self.bot_process.terminate()
            self.bot_process.wait()

    def SvcDoRun(self):
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        # Get the directory where this script is located
        script_dir = Path(__file__).parent
        bot_script = script_dir / "bot.py"
        
        while self.is_alive:
            try:
                # Start the bot
                self.bot_process = subprocess.Popen([
                    sys.executable, str(bot_script)
                ], cwd=str(script_dir))
                
                servicemanager.LogMsg(
                    servicemanager.EVENTLOG_INFORMATION_TYPE,
                    servicemanager.PYS_SERVICE_STARTED,
                    (self._svc_name_, 'Bot process started')
                )
                
                # Wait for the process to complete or service to stop
                while self.is_alive and self.bot_process.poll() is None:
                    time.sleep(1)
                
                if self.is_alive and self.bot_process.poll() is not None:
                    # Bot crashed, restart it
                    servicemanager.LogMsg(
                        servicemanager.EVENTLOG_WARNING_TYPE,
                        servicemanager.PYS_SERVICE_STARTED,
                        (self._svc_name_, 'Bot crashed, restarting...')
                    )
                    time.sleep(5)  # Wait before restart
                    
            except Exception as e:
                servicemanager.LogMsg(
                    servicemanager.EVENTLOG_ERROR_TYPE,
                    servicemanager.PYS_SERVICE_STARTED,
                    (self._svc_name_, f'Error: {str(e)}')
                )
                time.sleep(10)

if __name__ == '__main__':
    if len(sys.argv) == 1:
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(ChoirBotService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        win32serviceutil.HandleCommandLine(ChoirBotService)
