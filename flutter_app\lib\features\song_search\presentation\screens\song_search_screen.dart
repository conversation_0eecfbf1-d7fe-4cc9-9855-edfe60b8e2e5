import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/song_search_bloc.dart';

class SongSearchScreen extends StatefulWidget {
  const SongSearchScreen({Key? key}) : super(key: key);

  @override
  State<SongSearchScreen> createState() => _SongSearchScreenState();
}

class _SongSearchScreenState extends State<SongSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String selectedCategory = 'all';

  final List<String> categories = [
    'all',
    'hymns',
    'lyrics',
    'conventions',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🎵 Song Search'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: BlocConsumer<SongSearchBloc, SongSearchState>(
        listener: (context, state) {
          if (state is SongSearchError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search Bar
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search songs, hymns, or lyrics...',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () => _searchController.clear(),
                            ),
                          ),
                          onSubmitted: (query) => _performSearch(query),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Text('Category: '),
                            DropdownButton<String>(
                              value: selectedCategory,
                              items: categories.map((category) {
                                return DropdownMenuItem(
                                  value: category,
                                  child: Text(category.toUpperCase()),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => selectedCategory = value);
                                }
                              },
                            ),
                            const Spacer(),
                            ElevatedButton(
                              onPressed: () => _performSearch(_searchController.text),
                              child: const Text('Search'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Content Area
                Expanded(
                  child: _buildContent(state),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(SongSearchState state) {
    if (state is SongSearchLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Searching...'),
          ],
        ),
      );
    } else if (state is SongSearchResults) {
      return _buildSearchResults(state);
    } else if (state is SongDetailsLoaded) {
      return _buildSongDetails(state.song);
    } else {
      return _buildWelcomeView();
    }
  }

  Widget _buildWelcomeView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.music_note,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Welcome to Song Search',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Search for hymns, lyrics, and convention songs.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Search Examples:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text('• "Amazing Grace" - Song title'),
            const Text('• "H-123" - Hymn number'),
            const Text('• "love" - Search lyrics'),
            const Text('• "Christmas" - Theme search'),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(SongSearchResults state) {
    if (state.songs.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No songs found',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'Try searching with different keywords.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Results for "${state.query}" (${state.songs.length} found)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: state.songs.length,
            itemBuilder: (context, index) {
              final song = state.songs[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4.0),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      song.number ?? '#',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    song.title,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Category: ${song.category}'),
                      if (song.theme != null) Text('Theme: ${song.theme}'),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.info_outline),
                    onPressed: () {
                      context.read<SongSearchBloc>().add(LoadSongDetailsEvent(song.id));
                    },
                  ),
                  onTap: () {
                    context.read<SongSearchBloc>().add(LoadSongDetailsEvent(song.id));
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSongDetails(song) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    song.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () {
                    // Go back to search results - you'd implement this
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (song.number != null)
              Text('Number: ${song.number}'),
            Text('Category: ${song.category}'),
            if (song.theme != null)
              Text('Theme: ${song.theme}'),
            const Divider(),
            if (song.lyrics != null) ...[
              Text(
                'Lyrics:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    song.lyrics!,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
              ),
            ] else ...[
              const Expanded(
                child: Center(
                  child: Text('Lyrics not available'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;
    
    context.read<SongSearchBloc>().add(SearchSongsEvent(
      query: query.trim(),
      category: selectedCategory,
    ));
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
