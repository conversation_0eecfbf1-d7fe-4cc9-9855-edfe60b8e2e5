# FastAPI Backend Requirements for Railway Choir Mobile App

# Core FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# CORS middleware (already included in FastAPI)
# fastapi includes starlette which has CORS support

# Data validation and serialization
pydantic==2.5.0

# HTTP client for external API calls (if needed)
httpx==0.25.2

# Additional dependencies that might be needed
python-multipart==0.0.6  # For form data handling
python-jose[cryptography]==3.3.0  # For JWT tokens (if implementing auth)

# All existing dependencies from your main requirements.txt
# (Copy the relevant ones from your main requirements.txt)

# Note: Make sure to also install your existing dependencies:
# - All the packages from your main requirements.txt
# - Especially: pandas, streamlit, google-api-python-client, etc.
