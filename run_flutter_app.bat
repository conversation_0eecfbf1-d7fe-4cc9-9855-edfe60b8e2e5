@echo off
echo 📱 Starting Railway Choir Flutter App
echo =====================================

echo.
echo 🔍 Checking Flutter installation...
C:\flutter\bin\flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found!
    pause
    exit /b 1
)

echo.
echo 📂 Navigating to Flutter app directory...
cd flutter_app
if not exist pubspec.yaml (
    echo ❌ Flutter app not found!
    echo 💡 Make sure you're in the correct directory
    pause
    exit /b 1
)

echo.
echo 📦 Getting dependencies...
C:\flutter\bin\flutter pub get

echo.
echo 🌐 Enabling web support...
C:\flutter\bin\flutter config --enable-web

echo.
echo 🔍 Checking available devices...
C:\flutter\bin\flutter devices

echo.
echo 🚀 Starting Flutter app...
echo 💡 This will open in your default browser
echo 📱 API Server should be running on localhost:8000
echo.

C:\flutter\bin\flutter run -d chrome --web-port 3000

pause
