# Flutter Setup Test Script
Write-Host "🔍 Testing Flutter Setup..." -ForegroundColor Cyan

# Test Flutter using full path
Write-Host "`n📱 Testing Flutter with full path..." -ForegroundColor Yellow
try {
    $flutterVersion = & "C:\flutter\bin\flutter.bat" --version 2>&1
    Write-Host "✅ Flutter found!" -ForegroundColor Green
    Write-Host $flutterVersion
} catch {
    Write-Host "❌ Flutter not found at C:\flutter\bin" -ForegroundColor Red
    exit 1
}

# Navigate to Flutter app directory
Write-Host "`n📂 Navigating to Flutter app..." -ForegroundColor Yellow
Set-Location "flutter_app"

if (-not (Test-Path "pubspec.yaml")) {
    Write-Host "❌ Flutter app not found!" -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "`n📦 Getting Flutter dependencies..." -ForegroundColor Yellow
try {
    & "C:\flutter\bin\flutter.bat" pub get
    Write-Host "✅ Dependencies installed!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get dependencies" -ForegroundColor Red
}

# Enable web
Write-Host "`n🌐 Enabling web support..." -ForegroundColor Yellow
try {
    & "C:\flutter\bin\flutter.bat" config --enable-web
    Write-Host "✅ Web support enabled!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to enable web support" -ForegroundColor Red
}

# Check devices
Write-Host "`n🔍 Checking available devices..." -ForegroundColor Yellow
try {
    & "C:\flutter\bin\flutter.bat" devices
} catch {
    Write-Host "❌ Failed to check devices" -ForegroundColor Red
}

Write-Host "`n🎉 Flutter setup test complete!" -ForegroundColor Green
Write-Host "🚀 To run the app: C:\flutter\bin\flutter.bat run -d chrome" -ForegroundColor Cyan
