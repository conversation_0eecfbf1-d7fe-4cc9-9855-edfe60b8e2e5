<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Railway Choir Mobile App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .app-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 1.5em;
            margin-bottom: 5px;
        }
        
        .status {
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.online {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status.offline {
            background: #ffeaea;
            color: #c62828;
        }
        
        .nav-tabs {
            display: flex;
            background: #f0f0f0;
            border-bottom: 1px solid #ddd;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .nav-tab.active {
            background: white;
            color: #1976D2;
            border-bottom: 2px solid #1976D2;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .button {
            background: #1976D2;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #1565C0;
        }
        
        .button.secondary {
            background: #666;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #1976D2;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .error {
            border-left-color: #f44336;
            background: #ffeaea;
        }
        
        .question-card {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .verse-text {
            font-size: 18px;
            font-style: italic;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .option-button {
            background: white;
            border: 2px solid #1976D2;
            color: #1976D2;
            padding: 15px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            text-align: left;
            transition: all 0.3s;
        }
        
        .option-button:hover {
            background: #1976D2;
            color: white;
        }
        
        .score-display {
            display: flex;
            justify-content: space-between;
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .score-item {
            text-align: center;
        }
        
        .score-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976D2;
        }
        
        .score-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>🎵 Railway Choir</h1>
            <p>Mobile App</p>
        </div>
        
        <div id="status" class="status offline">
            🔄 Checking connection...
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('game')">🎮 Game</button>
            <button class="nav-tab" onclick="showTab('bible')">📖 Bible</button>
            <button class="nav-tab" onclick="showTab('songs')">🎵 Songs</button>
            <button class="nav-tab" onclick="showTab('settings')">⚙️ Settings</button>
        </div>
        
        <!-- Bible Game Tab -->
        <div id="game" class="tab-content active">
            <div class="score-display">
                <div class="score-item">
                    <div class="score-value" id="gameScore">0/0</div>
                    <div class="score-label">Score</div>
                </div>
                <div class="score-item">
                    <div class="score-value" id="gameAccuracy">0%</div>
                    <div class="score-label">Accuracy</div>
                </div>
            </div>
            
            <div class="card">
                <div class="input-group">
                    <label>Language:</label>
                    <select id="gameLanguage">
                        <option value="english">🇺🇸 English</option>
                        <option value="malayalam">🇮🇳 Malayalam</option>
                    </select>
                </div>
                
                <div class="input-group">
                    <label>Difficulty:</label>
                    <select id="gameDifficulty">
                        <option value="Easy">🟢 Easy</option>
                        <option value="Medium">🟡 Medium</option>
                        <option value="Hard">🔴 Hard</option>
                    </select>
                </div>
                
                <button class="button" onclick="startGame()">🎮 Start New Game</button>
                <button class="button secondary" onclick="showLeaderboard()">🏅 Leaderboard</button>
            </div>
            
            <div id="gameQuestion" style="display: none;">
                <div class="question-card">
                    <div class="verse-text" id="verseText"></div>
                    <p><strong>Which Bible reference is this verse from?</strong></p>
                    <div id="gameOptions"></div>
                </div>
            </div>
            
            <div id="gameResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Bible Reading Tab -->
        <div id="bible" class="tab-content">
            <div class="card">
                <div class="input-group">
                    <label>Book:</label>
                    <input type="text" id="bibleBook" value="John" placeholder="e.g., John, Genesis">
                </div>
                
                <div class="input-group">
                    <label>Chapter:</label>
                    <input type="number" id="bibleChapter" value="3" placeholder="Chapter number">
                </div>
                
                <div class="input-group">
                    <label>Language:</label>
                    <select id="bibleLanguage">
                        <option value="english">English</option>
                        <option value="malayalam">Malayalam</option>
                        <option value="hindi">Hindi</option>
                        <option value="tamil">Tamil</option>
                    </select>
                </div>
                
                <button class="button" onclick="getChapter()">📖 Get Chapter</button>
                
                <div class="input-group">
                    <label>Search Verses:</label>
                    <input type="text" id="bibleSearch" placeholder="Search for verses...">
                </div>
                
                <button class="button secondary" onclick="searchBible()">🔍 Search</button>
            </div>
            
            <div id="bibleResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Songs Tab -->
        <div id="songs" class="tab-content">
            <div class="card">
                <div class="input-group">
                    <label>Search Songs:</label>
                    <input type="text" id="songSearch" placeholder="Search songs, hymns, lyrics...">
                </div>
                
                <div class="input-group">
                    <label>Category:</label>
                    <select id="songCategory">
                        <option value="all">All</option>
                        <option value="hymns">Hymns</option>
                        <option value="lyrics">Lyrics</option>
                        <option value="conventions">Conventions</option>
                    </select>
                </div>
                
                <button class="button" onclick="searchSongs()">🎵 Search Songs</button>
                <button class="button secondary" onclick="getSampleSong()">📄 Sample Song</button>
            </div>
            
            <div id="songResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="card">
                <h3>🔧 App Settings</h3>
                <div class="input-group">
                    <label>User ID:</label>
                    <input type="text" id="userId" value="mobile_user" placeholder="Enter your user ID">
                </div>
                
                <button class="button" onclick="checkHealth()">❤️ Check API Health</button>
                <button class="button secondary" onclick="openApiDocs()">📚 API Documentation</button>
                <button class="button secondary" onclick="clearAllResults()">🧹 Clear All Results</button>
            </div>
            
            <div class="card">
                <h3>📊 Statistics</h3>
                <p>Games Played: <span id="gamesPlayed">0</span></p>
                <p>Best Score: <span id="bestScore">0</span></p>
                <p>Favorite Language: <span id="favLanguage">English</span></p>
            </div>
            
            <div id="settingsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let currentQuestion = null;
        let gameStats = { score: 0, total: 0 };
        
        // Initialize app
        window.onload = function() {
            checkApiStatus();
        };
        
        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        // API functions
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: { 'Content-Type': 'application/json' }
                };
                
                if (data) options.body = JSON.stringify(data);
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                return { success: response.ok, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function checkApiStatus() {
            const result = await apiCall('/health');
            const statusDiv = document.getElementById('status');
            
            if (result.success) {
                statusDiv.className = 'status online';
                statusDiv.innerHTML = '✅ Connected to API';
            } else {
                statusDiv.className = 'status offline';
                statusDiv.innerHTML = '❌ API Offline - Start server: py simple_api_server.py';
            }
        }
        
        // Bible Game functions
        async function startGame() {
            const data = {
                user_id: document.getElementById('userId').value || 'mobile_user',
                language: document.getElementById('gameLanguage').value,
                difficulty: document.getElementById('gameDifficulty').value
            };
            
            const result = await apiCall('/bible-game/start', 'POST', data);
            
            if (result.success) {
                currentQuestion = result.data;
                displayQuestion(result.data);
                gameStats = { score: 0, total: 0 };
                updateScoreDisplay();
            } else {
                showResult('gameResult', result);
            }
        }
        
        function displayQuestion(question) {
            document.getElementById('verseText').textContent = `"${question.verse_text}"`;
            
            const optionsDiv = document.getElementById('gameOptions');
            optionsDiv.innerHTML = '';
            
            question.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'option-button';
                button.textContent = `${String.fromCharCode(65 + index)}) ${option}`;
                button.onclick = () => submitAnswer(option);
                optionsDiv.appendChild(button);
            });
            
            document.getElementById('gameQuestion').style.display = 'block';
        }
        
        async function submitAnswer(answer) {
            const data = {
                user_id: document.getElementById('userId').value || 'mobile_user',
                answer: answer
            };
            
            const result = await apiCall('/bible-game/answer', 'POST', data);
            
            if (result.success) {
                gameStats.total = result.data.total_questions;
                gameStats.score = result.data.score;
                updateScoreDisplay();
                
                const message = result.data.is_correct ? 
                    `🎉 Correct! The answer is ${result.data.correct_answer}` :
                    `❌ Wrong! The correct answer is ${result.data.correct_answer}`;
                
                alert(message);
                
                if (result.data.next_question) {
                    displayQuestion(result.data.next_question);
                } else {
                    document.getElementById('gameQuestion').style.display = 'none';
                    alert(`Game Over! Final Score: ${gameStats.score}/${gameStats.total}`);
                }
            } else {
                showResult('gameResult', result);
            }
        }
        
        function updateScoreDisplay() {
            document.getElementById('gameScore').textContent = `${gameStats.score}/${gameStats.total}`;
            const accuracy = gameStats.total > 0 ? (gameStats.score / gameStats.total * 100).toFixed(1) : 0;
            document.getElementById('gameAccuracy').textContent = `${accuracy}%`;
        }
        
        async function showLeaderboard() {
            const difficulty = document.getElementById('gameDifficulty').value;
            const result = await apiCall(`/bible-game/leaderboard?difficulty=${difficulty}&limit=5`);
            showResult('gameResult', result);
        }
        
        // Bible reading functions
        async function getChapter() {
            const book = document.getElementById('bibleBook').value;
            const chapter = document.getElementById('bibleChapter').value;
            const language = document.getElementById('bibleLanguage').value;
            
            const result = await apiCall(`/bible/${book}/${chapter}?language=${language}`);
            showResult('bibleResult', result);
        }
        
        async function searchBible() {
            const query = document.getElementById('bibleSearch').value;
            const language = document.getElementById('bibleLanguage').value;
            
            const result = await apiCall(`/bible/search?query=${encodeURIComponent(query)}&language=${language}&limit=10`);
            showResult('bibleResult', result);
        }
        
        // Song functions
        async function searchSongs() {
            const query = document.getElementById('songSearch').value;
            const category = document.getElementById('songCategory').value;
            
            const result = await apiCall(`/songs/search?query=${encodeURIComponent(query)}&category=${category}&limit=10`);
            showResult('songResult', result);
        }
        
        async function getSampleSong() {
            const result = await apiCall('/songs/H-1');
            showResult('songResult', result);
        }
        
        // Settings functions
        async function checkHealth() {
            const result = await apiCall('/health');
            showResult('settingsResult', result);
            checkApiStatus();
        }
        
        function openApiDocs() {
            window.open('http://localhost:8000/docs', '_blank');
        }
        
        function clearAllResults() {
            document.querySelectorAll('.result').forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
        }
        
        // Utility functions
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `Error: ${result.error || result.data?.detail || 'Unknown error'}`;
            }
        }
    </script>
</body>
</html>
