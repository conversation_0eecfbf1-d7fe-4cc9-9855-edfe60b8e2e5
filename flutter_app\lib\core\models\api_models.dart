import 'package:equatable/equatable.dart';

// Bible Game Models
class BibleQuestion extends Equatable {
  final String verseText;
  final String correctAnswer;
  final List<String> options;
  final String difficulty;
  final String language;
  final String reference;

  const BibleQuestion({
    required this.verseText,
    required this.correctAnswer,
    required this.options,
    required this.difficulty,
    required this.language,
    required this.reference,
  });

  factory BibleQuestion.fromJson(Map<String, dynamic> json) {
    return BibleQuestion(
      verseText: json['verse_text'] as String,
      correctAnswer: json['correct_answer'] as String,
      options: List<String>.from(json['options'] as List),
      difficulty: json['difficulty'] as String,
      language: json['language'] as String,
      reference: json['reference'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'verse_text': verseText,
      'correct_answer': correctAnswer,
      'options': options,
      'difficulty': difficulty,
      'language': language,
      'reference': reference,
    };
  }

  @override
  List<Object?> get props => [verseText, correctAnswer, options, difficulty, language, reference];
}

class GameAnswerResponse extends Equatable {
  final bool isCorrect;
  final String correctAnswer;
  final int score;
  final int totalQuestions;
  final BibleQuestion? nextQuestion;
  final String? message;

  const GameAnswerResponse({
    required this.isCorrect,
    required this.correctAnswer,
    required this.score,
    required this.totalQuestions,
    this.nextQuestion,
    this.message,
  });

  factory GameAnswerResponse.fromJson(Map<String, dynamic> json) {
    return GameAnswerResponse(
      isCorrect: json['is_correct'] as bool,
      correctAnswer: json['correct_answer'] as String,
      score: json['score'] as int,
      totalQuestions: json['total_questions'] as int,
      nextQuestion: json['next_question'] != null
          ? BibleQuestion.fromJson(json['next_question'] as Map<String, dynamic>)
          : null,
      message: json['message'] as String?,
    );
  }

  @override
  List<Object?> get props => [isCorrect, correctAnswer, score, totalQuestions, nextQuestion, message];
}

class LeaderboardEntry extends Equatable {
  final String userId;
  final String userName;
  final int score;
  final String difficulty;
  final DateTime date;

  const LeaderboardEntry({
    required this.userId,
    required this.userName,
    required this.score,
    required this.difficulty,
    required this.date,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntry(
      userId: json['user_id'] as String,
      userName: json['user_name'] as String,
      score: json['score'] as int,
      difficulty: json['difficulty'] as String,
      date: DateTime.parse(json['date'] as String),
    );
  }

  @override
  List<Object?> get props => [userId, userName, score, difficulty, date];
}

// Bible Reading Models
class BibleChapter extends Equatable {
  final String book;
  final int chapter;
  final String language;
  final List<BibleVerse> verses;
  final String url;

  const BibleChapter({
    required this.book,
    required this.chapter,
    required this.language,
    required this.verses,
    required this.url,
  });

  factory BibleChapter.fromJson(Map<String, dynamic> json) {
    return BibleChapter(
      book: json['book'] as String,
      chapter: json['chapter'] as int,
      language: json['language'] as String,
      verses: (json['verses'] as List)
          .map((verse) => BibleVerse.fromJson(verse as Map<String, dynamic>))
          .toList(),
      url: json['url'] as String,
    );
  }

  @override
  List<Object?> get props => [book, chapter, language, verses, url];
}

class BibleVerse extends Equatable {
  final int number;
  final String text;

  const BibleVerse({
    required this.number,
    required this.text,
  });

  factory BibleVerse.fromJson(Map<String, dynamic> json) {
    return BibleVerse(
      number: json['number'] as int,
      text: json['text'] as String,
    );
  }

  @override
  List<Object?> get props => [number, text];
}

class BibleSearchResult extends Equatable {
  final String book;
  final int chapter;
  final int verse;
  final String text;
  final String language;
  final String reference;

  const BibleSearchResult({
    required this.book,
    required this.chapter,
    required this.verse,
    required this.text,
    required this.language,
    required this.reference,
  });

  factory BibleSearchResult.fromJson(Map<String, dynamic> json) {
    return BibleSearchResult(
      book: json['book'] as String,
      chapter: json['chapter'] as int,
      verse: json['verse'] as int,
      text: json['text'] as String,
      language: json['language'] as String,
      reference: json['reference'] as String,
    );
  }

  @override
  List<Object?> get props => [book, chapter, verse, text, language, reference];
}

// Song Models
class Song extends Equatable {
  final String id;
  final String title;
  final String? number;
  final String category;
  final String? theme;
  final String? lyrics;
  final String? tune;
  final DateTime? lastSung;
  final List<String>? tunes;

  const Song({
    required this.id,
    required this.title,
    this.number,
    required this.category,
    this.theme,
    this.lyrics,
    this.tune,
    this.lastSung,
    this.tunes,
  });

  factory Song.fromJson(Map<String, dynamic> json) {
    return Song(
      id: json['id'] as String,
      title: json['title'] as String,
      number: json['number'] as String?,
      category: json['category'] as String,
      theme: json['theme'] as String?,
      lyrics: json['lyrics'] as String?,
      tune: json['tune'] as String?,
      lastSung: json['last_sung'] != null 
          ? DateTime.parse(json['last_sung'] as String)
          : null,
      tunes: json['tunes'] != null 
          ? List<String>.from(json['tunes'] as List)
          : null,
    );
  }

  @override
  List<Object?> get props => [id, title, number, category, theme, lyrics, tune, lastSung, tunes];
}

// Music Notation Models
class MusicNotation extends Equatable {
  final String hymnNumber;
  final Map<String, String> notations; // tune name -> image URL/path

  const MusicNotation({
    required this.hymnNumber,
    required this.notations,
  });

  factory MusicNotation.fromJson(Map<String, dynamic> json) {
    return MusicNotation(
      hymnNumber: json['hymn_number'] as String,
      notations: Map<String, String>.from(json['notations'] as Map),
    );
  }

  @override
  List<Object?> get props => [hymnNumber, notations];
}
