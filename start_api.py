#!/usr/bin/env python3
"""
Startup script for Railway Choir API Server
This script ensures all dependencies are loaded before starting the FastAPI server
"""

import sys
import os
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'pandas',
        'streamlit'  # From your existing requirements
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("📦 Please install them with: pip install fastapi uvicorn pydantic")
        return False
    
    return True

def setup_environment():
    """Setup environment variables and paths"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set environment variables if needed
    os.environ.setdefault('PYTHONPATH', str(current_dir))
    
    print("✅ Environment setup complete")

def start_server():
    """Start the FastAPI server"""
    try:
        print("🚀 Starting Railway Choir API Server...")
        print("📱 Flutter app can connect to: http://localhost:8000")
        print("📚 API documentation: http://localhost:8000/docs")
        print("🔄 Server will auto-reload on code changes")
        print("⏹️  Press Ctrl+C to stop the server")
        print("-" * 50)
        
        # Import and run the server
        import uvicorn
        uvicorn.run(
            "api_server:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n⏹️  Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

def main():
    """Main function"""
    print("🎵 Railway Choir API Server Startup")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Start server
    start_server()

if __name__ == "__main__":
    main()
