# 🚀 Railway Choir App Deployment Guide

This guide covers deploying both the FastAPI backend and Flutter mobile app.

## 📋 Prerequisites

- Python 3.8+
- Flutter SDK 3.0+
- Git
- Railway account (for backend deployment)
- Google Play Console / Apple Developer account (for app store deployment)

## 🔧 Backend Deployment (FastAPI)

### Option 1: Railway Deployment (Recommended)

1. **Prepare for Railway**
   ```bash
   # Create railway.json
   echo '{"build": {"command": "pip install -r requirements.txt"}, "start": {"command": "python api_server.py"}}' > railway.json
   ```

2. **Deploy to Railway**
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login and deploy
   railway login
   railway init
   railway up
   ```

3. **Environment Variables**
   Set these in Railway dashboard:
   ```
   PYTHONPATH=/app
   PORT=8000
   ```

### Option 2: Heroku Deployment

1. **Create Procfile**
   ```
   web: python api_server.py
   ```

2. **Deploy**
   ```bash
   heroku create your-choir-api
   git push heroku main
   ```

### Option 3: Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM python:3.9-slim
   
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   
   COPY . .
   
   EXPOSE 8000
   CMD ["python", "api_server.py"]
   ```

2. **Build and run**
   ```bash
   docker build -t choir-api .
   docker run -p 8000:8000 choir-api
   ```

## 📱 Flutter App Deployment

### Android Deployment

1. **Configure API URL**
   Update `flutter_app/lib/core/services/api_service.dart`:
   ```dart
   static const String _productionUrl = 'https://your-deployed-api.railway.app/api';
   static const bool _isDevelopment = false; // Set to false for production
   ```

2. **Build APK**
   ```bash
   cd flutter_app
   flutter build apk --release
   ```

3. **Build App Bundle (for Play Store)**
   ```bash
   flutter build appbundle --release
   ```

4. **Upload to Google Play Console**
   - Go to Google Play Console
   - Upload the `.aab` file from `build/app/outputs/bundle/release/`

### iOS Deployment

1. **Configure for iOS**
   ```bash
   cd flutter_app
   flutter build ios --release
   ```

2. **Open in Xcode**
   ```bash
   open ios/Runner.xcworkspace
   ```

3. **Archive and Upload**
   - In Xcode: Product → Archive
   - Upload to App Store Connect

## 🧪 Testing the Deployment

### Test Backend API

1. **Run API tests**
   ```bash
   python test_api.py
   ```

2. **Manual testing**
   - Visit: `https://your-api-url.com/docs`
   - Test endpoints in the interactive documentation

### Test Flutter App

1. **Local testing with deployed API**
   ```bash
   cd flutter_app
   flutter run
   ```

2. **Test on physical devices**
   ```bash
   flutter install --release
   ```

## 🔒 Security Considerations

### Backend Security

1. **CORS Configuration**
   Update `api_server.py` for production:
   ```python
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["https://your-app-domain.com"],  # Specific domains only
       allow_credentials=True,
       allow_methods=["GET", "POST"],
       allow_headers=["*"],
   )
   ```

2. **Rate Limiting**
   Add rate limiting middleware:
   ```python
   from slowapi import Limiter, _rate_limit_exceeded_handler
   from slowapi.util import get_remote_address
   
   limiter = Limiter(key_func=get_remote_address)
   app.state.limiter = limiter
   app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
   ```

3. **Environment Variables**
   Store sensitive data in environment variables:
   ```python
   import os
   DATABASE_URL = os.getenv("DATABASE_URL")
   SECRET_KEY = os.getenv("SECRET_KEY")
   ```

### App Security

1. **API Key Management**
   Store API keys securely in Flutter:
   ```dart
   // Use flutter_dotenv for environment variables
   await dotenv.load(fileName: ".env");
   final apiKey = dotenv.env['API_KEY'];
   ```

2. **Certificate Pinning**
   Implement certificate pinning for API calls

## 📊 Monitoring and Analytics

### Backend Monitoring

1. **Add logging**
   ```python
   import logging
   logging.basicConfig(level=logging.INFO)
   logger = logging.getLogger(__name__)
   ```

2. **Health checks**
   The API includes a `/api/health` endpoint for monitoring

### App Analytics

1. **Firebase Analytics**
   ```bash
   flutter pub add firebase_analytics
   ```

2. **Crashlytics**
   ```bash
   flutter pub add firebase_crashlytics
   ```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy-api:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Railway
        run: |
          npm install -g @railway/cli
          railway deploy

  build-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - name: Build APK
        run: |
          cd flutter_app
          flutter build apk --release
```

## 🐛 Troubleshooting

### Common Backend Issues

1. **Import errors**
   - Ensure all dependencies are in requirements.txt
   - Check PYTHONPATH is set correctly

2. **Database connection issues**
   - Verify Google Sheets API credentials
   - Check network connectivity

3. **CORS errors**
   - Update CORS settings in api_server.py
   - Verify Flutter app is using correct API URL

### Common Flutter Issues

1. **API connection errors**
   - Check API URL in api_service.dart
   - Verify network permissions in AndroidManifest.xml

2. **Build errors**
   - Run `flutter clean` and `flutter pub get`
   - Check Flutter and Dart SDK versions

## 📞 Support

For deployment issues:
1. Check the logs in your deployment platform
2. Test API endpoints using the test script
3. Verify all environment variables are set
4. Check network connectivity and firewall settings

---

**🎉 Congratulations! Your Railway Choir app is now deployed and ready for users!**
