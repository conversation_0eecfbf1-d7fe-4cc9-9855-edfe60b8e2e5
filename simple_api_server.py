#!/usr/bin/env python3
"""
Simplified FastAPI Backend for Railway Choir Mobile App
This version starts quickly without heavy dataset loading
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import uuid
from datetime import datetime
import random

# Initialize FastAPI app
app = FastAPI(
    title="Railway Choir API",
    description="Backend API for Railway Choir Mobile App",
    version="1.0.0"
)

# Add CORS middleware for Flutter app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your Flutter app's domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for demo
game_sessions: Dict[str, Dict[str, Any]] = {}
sample_songs = [
    {"id": "H-1", "title": "Amazing Grace", "category": "hymns", "theme": "Grace"},
    {"id": "H-2", "title": "How Great Thou Art", "category": "hymns", "theme": "Worship"},
    {"id": "L-1", "title": "Blessed Assurance", "category": "lyrics", "theme": "Faith"},
]

# Pydantic models
class GameStartRequest(BaseModel):
    user_id: str
    language: str
    difficulty: str

class GameAnswerRequest(BaseModel):
    user_id: str
    answer: str

class BibleQuestion(BaseModel):
    verse_text: str
    correct_answer: str
    options: List[str]
    difficulty: str
    language: str
    reference: str

class GameAnswerResponse(BaseModel):
    is_correct: bool
    correct_answer: str
    score: int
    total_questions: int
    next_question: Optional[BibleQuestion] = None
    message: Optional[str] = None

class LeaderboardEntry(BaseModel):
    user_id: str
    user_name: str
    score: int
    difficulty: str
    date: str

class BibleVerse(BaseModel):
    number: int
    text: str

class BibleChapter(BaseModel):
    book: str
    chapter: int
    language: str
    verses: List[BibleVerse]
    url: str

class BibleSearchResult(BaseModel):
    book: str
    chapter: int
    verse: int
    text: str
    language: str
    reference: str

class Song(BaseModel):
    id: str
    title: str
    number: Optional[str] = None
    category: str
    theme: Optional[str] = None
    lyrics: Optional[str] = None
    tune: Optional[str] = None
    last_sung: Optional[str] = None
    tunes: Optional[List[str]] = None

# Sample Bible verses for demo
SAMPLE_VERSES = {
    "Easy": [
        {
            "verse_text": "For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.",
            "reference": "John 3:16",
            "book": "John",
            "chapter": 3,
            "verse": 16
        },
        {
            "verse_text": "The Lord is my shepherd, I lack nothing.",
            "reference": "Psalm 23:1",
            "book": "Psalm",
            "chapter": 23,
            "verse": 1
        }
    ],
    "Medium": [
        {
            "verse_text": "Trust in the Lord with all your heart and lean not on your own understanding.",
            "reference": "Proverbs 3:5",
            "book": "Proverbs",
            "chapter": 3,
            "verse": 5
        }
    ],
    "Hard": [
        {
            "verse_text": "And we know that in all things God works for the good of those who love him, who have been called according to his purpose.",
            "reference": "Romans 8:28",
            "book": "Romans",
            "chapter": 8,
            "verse": 28
        }
    ]
}

def create_sample_question(difficulty: str, language: str) -> BibleQuestion:
    """Create a sample Bible question"""
    verses = SAMPLE_VERSES.get(difficulty, SAMPLE_VERSES["Easy"])
    selected_verse = random.choice(verses)
    
    # Generate wrong options
    all_refs = ["John 3:16", "Psalm 23:1", "Proverbs 3:5", "Romans 8:28", "Matthew 5:16", "1 Corinthians 13:4"]
    wrong_options = [ref for ref in all_refs if ref != selected_verse["reference"]]
    wrong_options = random.sample(wrong_options, 3)
    
    options = [selected_verse["reference"]] + wrong_options
    random.shuffle(options)
    
    return BibleQuestion(
        verse_text=selected_verse["verse_text"],
        correct_answer=selected_verse["reference"],
        options=options,
        difficulty=difficulty,
        language=language,
        reference=selected_verse["reference"]
    )

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Railway Choir API - Simplified Version",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# Bible Game Endpoints
@app.post("/api/bible-game/start", response_model=BibleQuestion)
async def start_bible_game(request: GameStartRequest):
    """Start a new Bible game session"""
    try:
        # Reset user session
        game_sessions[request.user_id] = {
            "score": 0,
            "total_questions": 0,
            "current_difficulty": request.difficulty,
            "current_language": request.language,
            "current_question": None
        }
        
        # Create first question
        question = create_sample_question(request.difficulty, request.language)
        game_sessions[request.user_id]["current_question"] = question.correct_answer
        
        return question
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error starting game: {str(e)}")

@app.post("/api/bible-game/answer", response_model=GameAnswerResponse)
async def submit_bible_game_answer(request: GameAnswerRequest):
    """Submit an answer to the Bible game"""
    try:
        if request.user_id not in game_sessions:
            raise HTTPException(status_code=400, detail="No active game session")
        
        session = game_sessions[request.user_id]
        session["total_questions"] += 1
        
        # Check if answer is correct
        is_correct = request.answer == session.get("current_question")
        if is_correct:
            session["score"] += 1
        
        # Create next question
        next_question = create_sample_question(
            session["current_difficulty"], 
            session["current_language"]
        )
        session["current_question"] = next_question.correct_answer
        
        return GameAnswerResponse(
            is_correct=is_correct,
            correct_answer=session.get("current_question", "Unknown"),
            score=session["score"],
            total_questions=session["total_questions"],
            next_question=next_question,
            message="Great job!" if is_correct else "Keep trying!"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error submitting answer: {str(e)}")

@app.get("/api/bible-game/leaderboard", response_model=List[LeaderboardEntry])
async def get_bible_game_leaderboard(
    difficulty: Optional[str] = Query(None),
    limit: int = Query(10, ge=1, le=50)
):
    """Get Bible game leaderboard"""
    # Sample leaderboard data
    sample_leaderboard = [
        LeaderboardEntry(
            user_id="user1",
            user_name="John Doe",
            score=15,
            difficulty=difficulty or "Easy",
            date=datetime.now().isoformat()
        ),
        LeaderboardEntry(
            user_id="user2",
            user_name="Jane Smith",
            score=12,
            difficulty=difficulty or "Easy",
            date=datetime.now().isoformat()
        )
    ]
    
    return sample_leaderboard[:limit]

# Bible Reading Endpoints
@app.get("/api/bible/{book}/{chapter}", response_model=BibleChapter)
async def get_bible_chapter(book: str, chapter: int, language: str = "english"):
    """Get a Bible chapter"""
    # Sample chapter data
    sample_verses = [
        BibleVerse(number=1, text="In the beginning was the Word, and the Word was with God, and the Word was God."),
        BibleVerse(number=2, text="He was with God in the beginning."),
        BibleVerse(number=3, text="Through him all things were made; without him nothing was made that has been made.")
    ]
    
    return BibleChapter(
        book=book,
        chapter=chapter,
        language=language,
        verses=sample_verses,
        url=f"https://example.com/{book}/{chapter}"
    )

@app.get("/api/bible/search", response_model=List[BibleSearchResult])
async def search_bible(
    query: str = Query(..., min_length=1),
    language: str = Query("english"),
    limit: int = Query(50, ge=1, le=100)
):
    """Search Bible verses"""
    # Sample search results
    sample_results = [
        BibleSearchResult(
            book="John",
            chapter=3,
            verse=16,
            text="For God so loved the world...",
            language=language,
            reference="John 3:16"
        )
    ]
    
    return sample_results[:limit]

# Song Search Endpoints
@app.get("/api/songs/search", response_model=List[Song])
async def search_songs(
    query: str = Query(..., min_length=1),
    category: str = Query("all"),
    limit: int = Query(50, ge=1, le=100)
):
    """Search for songs"""
    # Filter sample songs based on query
    filtered_songs = [
        Song(**song) for song in sample_songs 
        if query.lower() in song["title"].lower()
    ]
    
    return filtered_songs[:limit]

@app.get("/api/songs/{song_id}", response_model=Song)
async def get_song_details(song_id: str):
    """Get song details"""
    # Find song in sample data
    for song in sample_songs:
        if song["id"] == song_id:
            return Song(**song)
    
    raise HTTPException(status_code=404, detail="Song not found")

if __name__ == "__main__":
    print("🚀 Starting Railway Choir API Server (Simplified)...")
    print("📱 Flutter app can connect to: http://localhost:8000")
    print("📚 API documentation: http://localhost:8000/docs")
    print("⚡ This is a simplified version for quick testing")
    
    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
