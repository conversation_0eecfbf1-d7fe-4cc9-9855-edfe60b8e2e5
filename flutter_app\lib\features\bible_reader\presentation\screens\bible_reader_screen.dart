import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bible_reader_bloc.dart';

class BibleReaderScreen extends StatefulWidget {
  const BibleReaderScreen({Key? key}) : super(key: key);

  @override
  State<BibleReaderScreen> createState() => _BibleReaderScreenState();
}

class _BibleReaderScreenState extends State<BibleReaderScreen> {
  final TextEditingController _searchController = TextEditingController();
  String selectedLanguage = 'malayalam';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('📖 Bible Reader'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: BlocConsumer<BibleReaderBloc, BibleReaderState>(
        listener: (context, state) {
          if (state is BibleReaderError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search Bar
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search Bible (e.g., "John 3:16" or "love")',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () => _searchController.clear(),
                            ),
                          ),
                          onSubmitted: (query) => _performSearch(query),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Text('Language: '),
                            DropdownButton<String>(
                              value: selectedLanguage,
                              items: const [
                                DropdownMenuItem(value: 'malayalam', child: Text('Malayalam')),
                                DropdownMenuItem(value: 'english', child: Text('English')),
                                DropdownMenuItem(value: 'hindi', child: Text('Hindi')),
                                DropdownMenuItem(value: 'tamil', child: Text('Tamil')),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => selectedLanguage = value);
                                }
                              },
                            ),
                            const Spacer(),
                            ElevatedButton(
                              onPressed: () => _performSearch(_searchController.text),
                              child: const Text('Search'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Content Area
                Expanded(
                  child: _buildContent(state),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(BibleReaderState state) {
    if (state is BibleReaderLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      );
    } else if (state is BibleChapterLoaded) {
      return _buildChapterView(state.chapter);
    } else if (state is BibleSearchResults) {
      return _buildSearchResults(state);
    } else {
      return _buildWelcomeView();
    }
  }

  Widget _buildWelcomeView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Welcome to Bible Reader',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Search for Bible verses or chapters using the search bar above.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Examples:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text('• "John 3:16" - Specific verse'),
            const Text('• "Genesis 1" - Entire chapter'),
            const Text('• "love" - Search for word'),
            const Text('• "യോഹന്നാൻ 3" - Malayalam reference'),
          ],
        ),
      ),
    );
  }

  Widget _buildChapterView(chapter) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '${chapter.book} ${chapter.chapter}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.bookmark_add),
                  onPressed: () {
                    // Add bookmark functionality
                  },
                ),
              ],
            ),
            const Divider(),
            Expanded(
              child: ListView.builder(
                itemCount: chapter.verses.length,
                itemBuilder: (context, index) {
                  final verse = chapter.verses[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: RichText(
                      text: TextSpan(
                        style: Theme.of(context).textTheme.bodyLarge,
                        children: [
                          TextSpan(
                            text: '${verse.number} ',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                          TextSpan(text: verse.text),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(BibleSearchResults state) {
    if (state.results.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No results found',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'Try searching with different keywords or check your spelling.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Results for "${state.query}" (${state.results.length} found)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: state.results.length,
            itemBuilder: (context, index) {
              final result = state.results[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 4.0),
                child: ListTile(
                  title: Text(result.reference),
                  subtitle: Text(
                    result.text,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    // Navigate to full chapter
                    context.read<BibleReaderBloc>().add(LoadChapterEvent(
                      book: result.book,
                      chapter: result.chapter,
                      language: selectedLanguage,
                    ));
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) return;
    
    context.read<BibleReaderBloc>().add(SearchBibleEvent(
      query: query.trim(),
      language: selectedLanguage,
    ));
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
