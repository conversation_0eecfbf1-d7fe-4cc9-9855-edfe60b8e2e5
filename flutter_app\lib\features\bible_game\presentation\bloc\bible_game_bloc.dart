import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/services/api_service.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/models/api_models.dart';

// Events
abstract class BibleGameEvent extends Equatable {
  const BibleGameEvent();

  @override
  List<Object?> get props => [];
}

class StartGameEvent extends BibleGameEvent {
  final String language;
  final String difficulty;

  const StartGameEvent({
    required this.language,
    required this.difficulty,
  });

  @override
  List<Object?> get props => [language, difficulty];
}

class SubmitAnswerEvent extends BibleGameEvent {
  final String answer;

  const SubmitAnswerEvent(this.answer);

  @override
  List<Object?> get props => [answer];
}

class LoadLeaderboardEvent extends BibleGameEvent {
  final String? difficulty;

  const LoadLeaderboardEvent({this.difficulty});

  @override
  List<Object?> get props => [difficulty];
}

class ResetGameEvent extends BibleGameEvent {}

// States
abstract class BibleGameState extends Equatable {
  const BibleGameState();

  @override
  List<Object?> get props => [];
}

class BibleGameInitial extends BibleGameState {}

class BibleGameLoading extends BibleGameState {}

class BibleGameQuestion extends BibleGameState {
  final BibleQuestion question;
  final int score;
  final int totalQuestions;
  final String language;
  final String difficulty;

  const BibleGameQuestion({
    required this.question,
    required this.score,
    required this.totalQuestions,
    required this.language,
    required this.difficulty,
  });

  @override
  List<Object?> get props => [question, score, totalQuestions, language, difficulty];
}

class BibleGameAnswerResult extends BibleGameState {
  final bool isCorrect;
  final String correctAnswer;
  final int score;
  final int totalQuestions;
  final BibleQuestion? nextQuestion;
  final String? message;

  const BibleGameAnswerResult({
    required this.isCorrect,
    required this.correctAnswer,
    required this.score,
    required this.totalQuestions,
    this.nextQuestion,
    this.message,
  });

  @override
  List<Object?> get props => [isCorrect, correctAnswer, score, totalQuestions, nextQuestion, message];
}

class BibleGameLeaderboard extends BibleGameState {
  final List<LeaderboardEntry> entries;
  final String? difficulty;

  const BibleGameLeaderboard({
    required this.entries,
    this.difficulty,
  });

  @override
  List<Object?> get props => [entries, difficulty];
}

class BibleGameError extends BibleGameState {
  final String message;

  const BibleGameError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class BibleGameBloc extends Bloc<BibleGameEvent, BibleGameState> {
  final ApiService apiService;
  final StorageService storageService;

  BibleGameBloc({
    required this.apiService,
    required this.storageService,
  }) : super(BibleGameInitial()) {
    on<StartGameEvent>(_onStartGame);
    on<SubmitAnswerEvent>(_onSubmitAnswer);
    on<LoadLeaderboardEvent>(_onLoadLeaderboard);
    on<ResetGameEvent>(_onResetGame);
  }

  Future<void> _onStartGame(StartGameEvent event, Emitter<BibleGameState> emit) async {
    emit(BibleGameLoading());
    
    try {
      // Reset game data
      await storageService.setGameScore(0);
      await storageService.setGameTotal(0);
      
      final userId = storageService.userId ?? 'anonymous';
      
      final question = await apiService.startBibleGame(
        userId: userId,
        language: event.language,
        difficulty: event.difficulty,
      );

      emit(BibleGameQuestion(
        question: question,
        score: 0,
        totalQuestions: 0,
        language: event.language,
        difficulty: event.difficulty,
      ));
    } catch (e) {
      emit(BibleGameError('Failed to start game: $e'));
    }
  }

  Future<void> _onSubmitAnswer(SubmitAnswerEvent event, Emitter<BibleGameState> emit) async {
    if (state is! BibleGameQuestion) return;
    
    final currentState = state as BibleGameQuestion;
    emit(BibleGameLoading());
    
    try {
      final userId = storageService.userId ?? 'anonymous';
      
      final result = await apiService.submitBibleGameAnswer(
        userId: userId,
        answer: event.answer,
      );

      // Update local storage
      await storageService.setGameScore(result.score);
      await storageService.setGameTotal(result.totalQuestions);
      
      // Update best score if needed
      if (result.isCorrect) {
        await storageService.updateBestScore(currentState.difficulty, result.score);
      }

      emit(BibleGameAnswerResult(
        isCorrect: result.isCorrect,
        correctAnswer: result.correctAnswer,
        score: result.score,
        totalQuestions: result.totalQuestions,
        nextQuestion: result.nextQuestion,
        message: result.message,
      ));
    } catch (e) {
      emit(BibleGameError('Failed to submit answer: $e'));
    }
  }

  Future<void> _onLoadLeaderboard(LoadLeaderboardEvent event, Emitter<BibleGameState> emit) async {
    emit(BibleGameLoading());
    
    try {
      final entries = await apiService.getBibleGameLeaderboard(
        difficulty: event.difficulty,
        limit: 10,
      );

      emit(BibleGameLeaderboard(
        entries: entries,
        difficulty: event.difficulty,
      ));
    } catch (e) {
      emit(BibleGameError('Failed to load leaderboard: $e'));
    }
  }

  Future<void> _onResetGame(ResetGameEvent event, Emitter<BibleGameState> emit) async {
    await storageService.setGameScore(0);
    await storageService.setGameTotal(0);
    emit(BibleGameInitial());
  }
}
