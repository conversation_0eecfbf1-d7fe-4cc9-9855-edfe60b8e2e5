# Google API Client and authentication libraries
google-api-python-client
google-auth
google-auth-oauthlib
google-auth-httplib2
googleapis-common-protos

# Data manipulation and file handling
pandas>=1.3.0
XlsxWriter>=3.0.0
openpyxl>=3.0.0

# Machine Learning libraries
scikit-learn>=1.0.0

# Indic NLP library
indic-nlp-library

# Streamlit for the web app interface
streamlit>=1.28.0

# Telegram bot
python-telegram-bot==20.7

# Async support
nest_asyncio>=1.5.0

# System utilities
psutil>=5.8.0

# Audio downloader dependencies
yt-dlp>=2024.4.9
requests
spotdl>=4.2.0

# Web scraping for Bible text extraction
beautifulsoup4>=4.9.0

# Additional dependencies that might be needed
httplib2==0.22.0
cachetools==5.3.2
pyasn1==0.5.0
pyasn1-modules==0.3.0
rsa==4.9
six==1.16.0
uritemplate==4.1.1
pytz

# New requirements
sentence-transformers

# Added from the code block
rapidfuzz

# Added from the code block
mutagen
