@echo off
echo 📱 Flutter Setup Helper for Railway Choir App
echo =============================================

echo.
echo 🔍 Searching for Flutter installation...

REM Check common Flutter installation locations
set FLUTTER_PATHS=C:\flutter\bin C:\src\flutter\bin %USERPROFILE%\flutter\bin %LOCALAPPDATA%\flutter\bin

for %%P in (%FLUTTER_PATHS%) do (
    if exist "%%P\flutter.bat" (
        echo ✅ Found Flutter at: %%P
        set FLUTTER_BIN=%%P
        goto :found_flutter
    )
)

echo ❌ Flutter not found in common locations
echo 💡 Please tell me where you installed Flutter:
echo    Common locations:
echo    - C:\flutter\bin
echo    - C:\src\flutter\bin
echo    - %USERPROFILE%\flutter\bin
echo.
set /p FLUTTER_BIN="Enter Flutter bin directory path: "

if not exist "%FLUTTER_BIN%\flutter.bat" (
    echo ❌ Flutter not found at: %FLUTTER_BIN%
    echo 💡 Please check the path and try again
    pause
    exit /b 1
)

:found_flutter
echo.
echo 🔧 Setting up Flutter...

REM Test Flutter
echo 🧪 Testing Flutter...
"%FLUTTER_BIN%\flutter.bat" --version
if %errorlevel% neq 0 (
    echo ❌ Flutter test failed
    pause
    exit /b 1
)

echo.
echo ✅ Flutter is working!

REM Add to PATH for current session
set PATH=%FLUTTER_BIN%;%PATH%

echo.
echo 🔧 Adding Flutter to system PATH...
setx PATH "%PATH%;%FLUTTER_BIN%" /M >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter added to system PATH
    echo 💡 You may need to restart your command prompt
) else (
    echo ⚠️  Could not add to system PATH (need admin rights)
    echo 💡 Please add manually: %FLUTTER_BIN%
)

echo.
echo 🩺 Running Flutter Doctor...
"%FLUTTER_BIN%\flutter.bat" doctor

echo.
echo 📱 Setting up Railway Choir Flutter app...
cd flutter_app
if not exist pubspec.yaml (
    echo ❌ Flutter app not found!
    echo 💡 Make sure you're in the correct directory
    pause
    exit /b 1
)

echo 📦 Getting Flutter dependencies...
"%FLUTTER_BIN%\flutter.bat" pub get

echo.
echo 🎉 Flutter setup complete!
echo.
echo 📱 Next steps:
echo 1. Make sure API server is running: py simple_api_server.py
echo 2. Connect Android device or start emulator
echo 3. Run: flutter run
echo.
echo 📚 Useful commands:
echo    flutter devices          - List available devices
echo    flutter emulators        - List available emulators  
echo    flutter run             - Run the app
echo    flutter doctor          - Check setup
echo.

pause
