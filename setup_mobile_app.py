#!/usr/bin/env python3
"""
Complete setup script for Railway Choir Mobile App
This script sets up both the FastAPI backend and Flutter frontend
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a shell command"""
    try:
        print(f"🔄 Running: {command}")
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(f"✅ Output: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"📄 Error details: {e.stderr}")
        return None

def check_python():
    """Check Python installation"""
    print("🐍 Checking Python installation...")
    
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
        python_version = result.stdout.strip()
        print(f"✅ {python_version}")
        
        # Check if Python 3.8+
        version_parts = python_version.split()[1].split('.')
        major, minor = int(version_parts[0]), int(version_parts[1])
        
        if major < 3 or (major == 3 and minor < 8):
            print("❌ Python 3.8+ is required")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Python check failed: {e}")
        return False

def check_flutter():
    """Check Flutter installation"""
    print("📱 Checking Flutter installation...")
    
    try:
        result = subprocess.run(["flutter", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Flutter is installed")
            print(result.stdout.split('\n')[0])  # First line has version info
            return True
        else:
            print("❌ Flutter not found")
            return False
    except FileNotFoundError:
        print("❌ Flutter not found in PATH")
        return False

def setup_backend():
    """Setup FastAPI backend"""
    print("\n🔧 Setting up FastAPI Backend...")
    
    # Install Python dependencies
    print("📦 Installing Python dependencies...")
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found")
        return False
    
    # Install requirements
    result = run_command(f"{sys.executable} -m pip install fastapi uvicorn pydantic")
    if not result:
        return False
    
    # Try to install existing requirements
    run_command(f"{sys.executable} -m pip install -r requirements.txt", check=False)
    
    print("✅ Backend dependencies installed")
    return True

def setup_flutter():
    """Setup Flutter app"""
    print("\n📱 Setting up Flutter App...")
    
    flutter_dir = Path("flutter_app")
    if not flutter_dir.exists():
        print("❌ flutter_app directory not found")
        return False
    
    # Get Flutter dependencies
    result = run_command("flutter pub get", cwd=flutter_dir)
    if not result:
        return False
    
    # Check for any issues
    result = run_command("flutter doctor", cwd=flutter_dir, check=False)
    
    print("✅ Flutter app setup complete")
    return True

def test_setup():
    """Test the complete setup"""
    print("\n🧪 Testing Setup...")
    
    # Test API server (start it in background and test)
    print("🚀 Starting API server for testing...")
    
    # Start server in background
    api_process = subprocess.Popen([
        sys.executable, "start_api.py"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait a bit for server to start
    import time
    time.sleep(5)
    
    # Test API
    try:
        import requests
        response = requests.get("http://localhost:8000/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ API server is working")
        else:
            print("❌ API server test failed")
    except Exception as e:
        print(f"❌ API test failed: {e}")
    finally:
        # Stop the server
        api_process.terminate()
        api_process.wait()
    
    # Test Flutter app compilation
    print("📱 Testing Flutter app compilation...")
    flutter_dir = Path("flutter_app")
    result = run_command("flutter analyze", cwd=flutter_dir, check=False)
    
    if result and result.returncode == 0:
        print("✅ Flutter app analysis passed")
    else:
        print("⚠️  Flutter app has some issues (check above)")
    
    return True

def create_run_scripts():
    """Create convenient run scripts"""
    print("\n📝 Creating run scripts...")
    
    # Create run_backend script
    backend_script = """#!/usr/bin/env python3
# Quick script to run the backend API server
import subprocess
import sys

if __name__ == "__main__":
    print("🚀 Starting Railway Choir API Server...")
    subprocess.run([sys.executable, "start_api.py"])
"""
    
    with open("run_backend.py", "w") as f:
        f.write(backend_script)
    
    # Create run_flutter script
    flutter_script = """#!/bin/bash
# Quick script to run the Flutter app
echo "📱 Starting Flutter app..."
cd flutter_app
flutter run
"""
    
    with open("run_flutter.sh", "w") as f:
        f.write(flutter_script)
    
    # Make scripts executable on Unix systems
    if platform.system() != "Windows":
        os.chmod("run_backend.py", 0o755)
        os.chmod("run_flutter.sh", 0o755)
    
    print("✅ Run scripts created:")
    print("   - run_backend.py (Start API server)")
    print("   - run_flutter.sh (Start Flutter app)")

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 Setup Complete! Next Steps:")
    print("=" * 60)
    
    print("\n1. 🚀 Start the Backend API:")
    print("   python run_backend.py")
    print("   or")
    print("   python start_api.py")
    
    print("\n2. 📱 Start the Flutter App:")
    if platform.system() == "Windows":
        print("   cd flutter_app && flutter run")
    else:
        print("   ./run_flutter.sh")
        print("   or")
        print("   cd flutter_app && flutter run")
    
    print("\n3. 🧪 Test the API:")
    print("   python test_api.py")
    
    print("\n4. 📚 View API Documentation:")
    print("   http://localhost:8000/docs")
    
    print("\n5. 🚀 Deploy (when ready):")
    print("   See DEPLOYMENT_GUIDE.md for detailed instructions")
    
    print("\n💡 Tips:")
    print("   - Start the backend first, then the Flutter app")
    print("   - Use 'flutter run' with a device/emulator connected")
    print("   - Check 'flutter doctor' if you have issues")
    print("   - API runs on http://localhost:8000")
    
    print("\n📞 Need help?")
    print("   - Check the README.md files")
    print("   - Run 'flutter doctor' for Flutter issues")
    print("   - Check API logs for backend issues")

def main():
    """Main setup function"""
    print("🎵 Railway Choir Mobile App Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python():
        print("❌ Python 3.8+ is required. Please install it first.")
        sys.exit(1)
    
    if not check_flutter():
        print("❌ Flutter is required. Please install it first.")
        print("💡 Visit: https://flutter.dev/docs/get-started/install")
        sys.exit(1)
    
    # Setup backend
    if not setup_backend():
        print("❌ Backend setup failed")
        sys.exit(1)
    
    # Setup Flutter
    if not setup_flutter():
        print("❌ Flutter setup failed")
        sys.exit(1)
    
    # Test setup
    test_setup()
    
    # Create run scripts
    create_run_scripts()
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
