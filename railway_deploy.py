#!/usr/bin/env python3
"""
Railway Deployment Helper for Railway Choir API
This script helps deploy the API to Railway cloud platform
"""

import os
import json
import subprocess
import sys

def create_railway_files():
    """Create necessary files for Railway deployment"""
    
    # Create railway.json
    railway_config = {
        "build": {
            "command": "pip install -r requirements.txt"
        },
        "start": {
            "command": "python simple_api_server.py"
        }
    }
    
    with open("railway.json", "w") as f:
        json.dump(railway_config, f, indent=2)
    
    print("✅ Created railway.json")
    
    # Create Procfile (alternative)
    with open("Procfile", "w") as f:
        f.write("web: python simple_api_server.py\n")
    
    print("✅ Created Procfile")
    
    # Create requirements.txt for deployment
    deployment_requirements = """fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
requests==2.31.0
python-multipart==0.0.6
"""
    
    with open("deployment_requirements.txt", "w") as f:
        f.write(deployment_requirements)
    
    print("✅ Created deployment_requirements.txt")
    
    # Update simple_api_server.py for production
    print("✅ API server is already configured for Railway")

def check_git():
    """Check if git is initialized"""
    if not os.path.exists(".git"):
        print("🔧 Initializing git repository...")
        subprocess.run(["git", "init"], check=True)
        subprocess.run(["git", "add", "."], check=True)
        subprocess.run(["git", "commit", "-m", "Initial commit for Railway deployment"], check=True)
        print("✅ Git repository initialized")
    else:
        print("✅ Git repository already exists")

def install_railway_cli():
    """Install Railway CLI"""
    try:
        # Check if Railway CLI is already installed
        result = subprocess.run(["railway", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Railway CLI already installed")
            return True
    except FileNotFoundError:
        pass
    
    print("📦 Installing Railway CLI...")
    
    # Try to install via npm
    try:
        subprocess.run(["npm", "install", "-g", "@railway/cli"], check=True)
        print("✅ Railway CLI installed via npm")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # Try to install via PowerShell
    try:
        powershell_cmd = 'iwr "https://railway.app/install.ps1" -useb | iex'
        subprocess.run(["powershell", "-Command", powershell_cmd], check=True)
        print("✅ Railway CLI installed via PowerShell")
        return True
    except subprocess.CalledProcessError:
        pass
    
    print("❌ Failed to install Railway CLI automatically")
    print("💡 Please install manually:")
    print("   1. Go to: https://railway.app/cli")
    print("   2. Follow installation instructions")
    print("   3. Run: railway login")
    print("   4. Run: railway init")
    print("   5. Run: railway up")
    
    return False

def deploy_to_railway():
    """Deploy to Railway"""
    print("🚀 Deploying to Railway...")
    
    try:
        # Login to Railway
        print("🔐 Please login to Railway...")
        subprocess.run(["railway", "login"], check=True)
        
        # Initialize Railway project
        print("🏗️ Initializing Railway project...")
        subprocess.run(["railway", "init"], check=True)
        
        # Deploy
        print("🚀 Deploying...")
        subprocess.run(["railway", "up"], check=True)
        
        print("🎉 Deployment successful!")
        print("🔗 Your API is now live!")
        print("📚 Check Railway dashboard for the URL")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Deployment failed: {e}")
        print("💡 Try manual deployment:")
        print("   1. railway login")
        print("   2. railway init")
        print("   3. railway up")

def main():
    """Main deployment function"""
    print("🚂 Railway Choir API - Railway Deployment")
    print("=" * 50)
    
    # Create Railway files
    create_railway_files()
    
    # Check git
    try:
        check_git()
    except subprocess.CalledProcessError:
        print("❌ Git not found. Please install Git first.")
        return
    
    # Install Railway CLI
    if not install_railway_cli():
        return
    
    # Deploy
    deploy_to_railway()
    
    print("\n🎯 Next Steps:")
    print("1. 📱 Update Flutter app API URL to your Railway URL")
    print("2. 🧪 Test the deployed API")
    print("3. 📱 Build and deploy Flutter app")

if __name__ == "__main__":
    main()
