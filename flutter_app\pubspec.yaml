name: choir_app
description: Railway Choir Mobile Application - Bible reading, games, and song search

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # HTTP and API
  http: ^1.1.0
  dio: ^5.3.2
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  
  # UI and Images
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.7
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  
  # Notifications
  flutter_local_notifications: ^16.1.0
  
  # Sharing
  share_plus: ^7.2.1
  
  # Icons
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
