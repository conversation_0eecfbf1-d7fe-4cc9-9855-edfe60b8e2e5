# Railway Choir Mobile App

A Flutter mobile application for the Railway Choir community, featuring Bible reading, interactive games, and song search functionality.

## 🚀 Features

### 📖 Bible Reader
- Read Bible in multiple languages (Malayalam, English, Hindi, Tamil)
- Search verses and chapters
- Bookmark favorite verses
- Reading history tracking

### 🎮 Bible Game
- Interactive verse guessing game
- Multiple difficulty levels (Easy, Medium, Hard)
- Language selection (English & Malayalam)
- Score tracking and leaderboards
- Personal statistics

### 🎵 Song Search
- Search hymns, lyrics, and convention songs
- Filter by category and theme
- Favorite songs management
- Recent searches history

### ⚙️ Settings
- User profile management
- Language preferences
- Theme selection (Light/Dark/System)
- Notification settings
- Game statistics

## 🏗️ Architecture

The app follows **Clean Architecture** principles with **BLoC** state management:

```
lib/
├── core/
│   ├── models/          # API data models
│   ├── services/        # API and storage services
│   └── theme/           # App theming
├── features/
│   ├── bible_game/      # Bible game feature
│   ├── bible_reader/    # Bible reading feature
│   ├── home/            # Home dashboard
│   ├── settings/        # App settings
│   └── song_search/     # Song search feature
└── main.dart            # App entry point
```

### Feature Structure
Each feature follows this structure:
```
feature_name/
├── presentation/
│   ├── bloc/           # BLoC state management
│   ├── screens/        # UI screens
│   └── widgets/        # Reusable widgets
```

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK
- Android Studio / VS Code
- Android/iOS device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flutter_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API endpoint**
   Update the `baseUrl` in `lib/core/services/api_service.dart`:
   ```dart
   static const String baseUrl = 'YOUR_API_URL/api';
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🔧 Backend API Requirements

The app expects a REST API with the following endpoints:

### Bible Game
- `POST /api/bible-game/start` - Start new game
- `POST /api/bible-game/answer` - Submit answer
- `GET /api/bible-game/leaderboard` - Get leaderboard

### Bible Reader
- `GET /api/bible/{book}/{chapter}` - Get Bible chapter
- `GET /api/bible/search` - Search Bible verses

### Song Search
- `GET /api/songs/search` - Search songs
- `GET /api/songs/{id}` - Get song details
- `GET /api/songs/theme/{theme}` - Get songs by theme

### Music Notation
- `GET /api/notation/{hymnNumber}` - Get music notation

## 📱 Supported Platforms

- ✅ Android (API 21+)
- ✅ iOS (iOS 11+)
- 🔄 Web (coming soon)

## 🎨 UI/UX Features

- **Material Design 3** components
- **Dark/Light theme** support
- **Responsive design** for tablets
- **Smooth animations** and transitions
- **Accessibility** features
- **Offline support** for cached content

## 📦 Key Dependencies

- `flutter_bloc` - State management
- `dio` - HTTP client
- `shared_preferences` - Local storage
- `sqflite` - Local database
- `cached_network_image` - Image caching

## 🚀 Build & Deploy

### Android
```bash
flutter build apk --release
# or
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 🧪 Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/
```

## 📝 Development Notes

### State Management
- Uses **BLoC pattern** for predictable state management
- Each feature has its own BLoC for isolation
- Events and states are well-defined and documented

### Data Flow
```
UI → BLoC → API Service → Backend
UI ← BLoC ← API Service ← Backend
```

### Local Storage
- User preferences stored in SharedPreferences
- Game data and bookmarks cached locally
- Offline-first approach for better UX

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- GitHub Issues: [Create an issue](link-to-issues)

---

**Built with ❤️ for the Railway Choir community**
