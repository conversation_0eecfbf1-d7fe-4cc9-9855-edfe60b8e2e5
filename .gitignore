.streamlit/
*.json
user_log.txt
bot_log.txt
comments.txt
__pycache__/
tmp/
Workshop.ipynb

downloader_log.txt

# Download feature documentation and setup files
DOWNLOAD_FEATURE_SUMMARY.md
DOWNLOAD_SETUP_GUIDE.md
DOWNLOAD_FEATURE_README.md
test_downloader.py
test_ffmpeg.py
requirements_new.txt
requirements_backup.txt

# Test files
test_*.py
*_test.py

# Download temporary files and logs
temp/
download_log.txt
*.mp3
*.mp4
*.wav
*.m4a
audio_downloader_bot.py
audio_downloader_streamlit_app.py
audio_downloader_debug.py

STREAMLIT_CLOUD_SETUP.md