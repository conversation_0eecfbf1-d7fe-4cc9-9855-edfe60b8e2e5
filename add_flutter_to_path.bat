@echo off
echo 🔧 Adding Flutter to System PATH
echo =================================

echo.
echo 🔍 Checking if Flutter is already in PATH...
flutter --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Flutter is already in PATH!
    flutter --version
    goto :end
)

echo ❌ Flutter not found in PATH

echo.
echo 📂 Checking Flutter installation at C:\flutter\bin...
if not exist "C:\flutter\bin\flutter.bat" (
    echo ❌ Flutter not found at C:\flutter\bin
    echo 💡 Please make sure Flutter is installed at C:\flutter
    pause
    exit /b 1
)

echo ✅ Flutter found at C:\flutter\bin

echo.
echo 🔧 Adding C:\flutter\bin to system PATH...
echo 💡 This requires administrator privileges

REM Add to system PATH (requires admin)
setx PATH "%PATH%;C:\flutter\bin" /M >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Successfully added to system PATH
    echo 💡 Please restart your command prompt or IDE
) else (
    echo ⚠️  Could not add to system PATH (need admin rights)
    echo 💡 Adding to user PATH instead...
    
    REM Add to user PATH (doesn't require admin)
    setx PATH "%PATH%;C:\flutter\bin" >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ Successfully added to user PATH
        echo 💡 Please restart your command prompt or IDE
    ) else (
        echo ❌ Failed to add to PATH
        echo 💡 Please add manually:
        echo    1. Open System Properties
        echo    2. Go to Environment Variables
        echo    3. Add C:\flutter\bin to PATH
    )
)

echo.
echo 🔄 Setting PATH for current session...
set PATH=%PATH%;C:\flutter\bin

echo.
echo 🧪 Testing Flutter in current session...
C:\flutter\bin\flutter --version
if %errorlevel% == 0 (
    echo ✅ Flutter is working!
) else (
    echo ❌ Flutter test failed
)

:end
echo.
echo 🎯 Next steps:
echo 1. Restart your command prompt or IDE
echo 2. Test: flutter --version
echo 3. Run: flutter pub get (in flutter_app directory)
echo 4. Run: flutter run -d chrome
echo.

pause
