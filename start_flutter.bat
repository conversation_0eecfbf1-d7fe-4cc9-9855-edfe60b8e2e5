@echo off
echo Starting Railway Choir Flutter App
echo ===================================

echo.
echo Checking API server...
py -c "import requests; r=requests.get('http://localhost:8000/api/health'); print('API OK' if r.status_code==200 else 'API ERROR')" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: API server not running
    echo Please start: py simple_api_server.py
    pause
    exit /b 1
)

echo API server is running

echo.
echo Navigating to Flutter app directory...
cd flutter_app

echo.
echo Getting Flutter dependencies...
C:\flutter\bin\flutter.bat pub get

echo.
echo Enabling web support...
C:\flutter\bin\flutter.bat config --enable-web

echo.
echo Starting Flutter app...
echo This will open in your browser
echo App URL: http://localhost:3000
echo API URL: http://localhost:8000

C:\flutter\bin\flutter.bat run -d chrome --web-port 3000
