import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  final SharedPreferences _prefs;

  StorageService(this._prefs);

  // User preferences
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _preferredLanguageKey = 'preferred_language';
  static const String _themeKey = 'theme_mode';

  // Bible game data
  static const String _gameScoreKey = 'game_score';
  static const String _gameTotalKey = 'game_total';
  static const String _bestScoresKey = 'best_scores';

  // Bible reading data
  static const String _bookmarksKey = 'bookmarks';
  static const String _readingHistoryKey = 'reading_history';

  // Song data
  static const String _favoriteSongsKey = 'favorite_songs';
  static const String _recentSearchesKey = 'recent_searches';

  // User Management
  String? get userId => _prefs.getString(_userIdKey);
  Future<bool> setUserId(String userId) => _prefs.setString(_userIdKey, userId);

  String? get userName => _prefs.getString(_userNameKey);
  Future<bool> setUserName(String userName) => _prefs.setString(_userNameKey, userName);

  String get preferredLanguage => _prefs.getString(_preferredLanguageKey) ?? 'english';
  Future<bool> setPreferredLanguage(String language) => _prefs.setString(_preferredLanguageKey, language);

  String get themeMode => _prefs.getString(_themeKey) ?? 'system';
  Future<bool> setThemeMode(String theme) => _prefs.setString(_themeKey, theme);

  // Bible Game Data
  int get gameScore => _prefs.getInt(_gameScoreKey) ?? 0;
  Future<bool> setGameScore(int score) => _prefs.setInt(_gameScoreKey, score);

  int get gameTotal => _prefs.getInt(_gameTotalKey) ?? 0;
  Future<bool> setGameTotal(int total) => _prefs.setInt(_gameTotalKey, total);

  Map<String, int> get bestScores {
    final scoresJson = _prefs.getString(_bestScoresKey);
    if (scoresJson == null) return {'Easy': 0, 'Medium': 0, 'Hard': 0};
    
    try {
      final decoded = json.decode(scoresJson) as Map<String, dynamic>;
      return decoded.map((key, value) => MapEntry(key, value as int));
    } catch (e) {
      return {'Easy': 0, 'Medium': 0, 'Hard': 0};
    }
  }

  Future<bool> setBestScores(Map<String, int> scores) {
    return _prefs.setString(_bestScoresKey, json.encode(scores));
  }

  Future<bool> updateBestScore(String difficulty, int score) async {
    final currentScores = bestScores;
    if (score > (currentScores[difficulty] ?? 0)) {
      currentScores[difficulty] = score;
      return setBestScores(currentScores);
    }
    return true;
  }

  // Bible Reading Data
  List<String> get bookmarks {
    final bookmarksJson = _prefs.getStringList(_bookmarksKey);
    return bookmarksJson ?? [];
  }

  Future<bool> addBookmark(String reference) async {
    final currentBookmarks = bookmarks;
    if (!currentBookmarks.contains(reference)) {
      currentBookmarks.add(reference);
      return _prefs.setStringList(_bookmarksKey, currentBookmarks);
    }
    return true;
  }

  Future<bool> removeBookmark(String reference) async {
    final currentBookmarks = bookmarks;
    currentBookmarks.remove(reference);
    return _prefs.setStringList(_bookmarksKey, currentBookmarks);
  }

  List<Map<String, dynamic>> get readingHistory {
    final historyJson = _prefs.getString(_readingHistoryKey);
    if (historyJson == null) return [];
    
    try {
      final decoded = json.decode(historyJson) as List<dynamic>;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  Future<bool> addToReadingHistory(String book, int chapter, String language) async {
    final currentHistory = readingHistory;
    final entry = {
      'book': book,
      'chapter': chapter,
      'language': language,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    // Remove if already exists to avoid duplicates
    currentHistory.removeWhere((item) => 
        item['book'] == book && 
        item['chapter'] == chapter && 
        item['language'] == language);
    
    // Add to beginning
    currentHistory.insert(0, entry);
    
    // Keep only last 50 entries
    if (currentHistory.length > 50) {
      currentHistory.removeRange(50, currentHistory.length);
    }
    
    return _prefs.setString(_readingHistoryKey, json.encode(currentHistory));
  }

  // Song Data
  List<String> get favoriteSongs {
    return _prefs.getStringList(_favoriteSongsKey) ?? [];
  }

  Future<bool> addFavoriteSong(String songId) async {
    final currentFavorites = favoriteSongs;
    if (!currentFavorites.contains(songId)) {
      currentFavorites.add(songId);
      return _prefs.setStringList(_favoriteSongsKey, currentFavorites);
    }
    return true;
  }

  Future<bool> removeFavoriteSong(String songId) async {
    final currentFavorites = favoriteSongs;
    currentFavorites.remove(songId);
    return _prefs.setStringList(_favoriteSongsKey, currentFavorites);
  }

  bool isFavoriteSong(String songId) {
    return favoriteSongs.contains(songId);
  }

  List<String> get recentSearches {
    return _prefs.getStringList(_recentSearchesKey) ?? [];
  }

  Future<bool> addRecentSearch(String query) async {
    final currentSearches = recentSearches;
    
    // Remove if already exists
    currentSearches.remove(query);
    
    // Add to beginning
    currentSearches.insert(0, query);
    
    // Keep only last 20 searches
    if (currentSearches.length > 20) {
      currentSearches.removeRange(20, currentSearches.length);
    }
    
    return _prefs.setStringList(_recentSearchesKey, currentSearches);
  }

  Future<bool> clearRecentSearches() {
    return _prefs.remove(_recentSearchesKey);
  }

  // Clear all data
  Future<bool> clearAllData() async {
    return await _prefs.clear();
  }
}
