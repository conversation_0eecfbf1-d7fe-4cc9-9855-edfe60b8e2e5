@echo off
echo 📱 Flutter Installation Helper for Railway Choir App
echo =====================================================

echo.
echo 🔍 Checking if Flutter is already installed...
flutter --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Flutter is already installed!
    flutter --version
    goto :setup_app
)

echo ❌ Flutter not found. Let's install it!
echo.

echo 📥 Option 1: Manual Installation (Recommended)
echo 1. Go to: https://flutter.dev/docs/get-started/install/windows
echo 2. Download Flutter SDK ZIP file
echo 3. Extract to C:\flutter (or any location)
echo 4. Add C:\flutter\bin to your PATH
echo 5. Restart this script
echo.

echo 📦 Option 2: Using winget (if available)
echo Running: winget install Google.Flutter
winget install Google.Flutter

echo.
echo 🔄 Refreshing PATH...
call refreshenv

echo.
echo 🧪 Testing Flutter installation...
flutter --version
if %errorlevel% == 0 (
    echo ✅ Flutter installed successfully!
    goto :setup_app
) else (
    echo ❌ Flutter installation failed
    echo 💡 Please try manual installation from: https://flutter.dev/docs/get-started/install/windows
    pause
    exit /b 1
)

:setup_app
echo.
echo 🏗️ Setting up Flutter app...
cd flutter_app
if not exist pubspec.yaml (
    echo ❌ Flutter app directory not found!
    echo 💡 Make sure you're running this from the correct directory
    pause
    exit /b 1
)

echo 📦 Getting Flutter dependencies...
flutter pub get

echo 🩺 Running Flutter doctor...
flutter doctor

echo.
echo 🎉 Setup complete!
echo.
echo 📱 To run the Flutter app:
echo    1. Make sure the API server is running: py simple_api_server.py
echo    2. Connect an Android device or start an emulator
echo    3. Run: flutter run
echo.
echo 📚 Useful commands:
echo    flutter devices          - List available devices
echo    flutter emulators        - List available emulators
echo    flutter run             - Run the app
echo    flutter doctor          - Check setup
echo.

pause
