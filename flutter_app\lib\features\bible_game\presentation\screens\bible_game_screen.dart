import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bible_game_bloc.dart';
import '../widgets/language_selection_widget.dart';
import '../widgets/difficulty_selection_widget.dart';
import '../widgets/game_stats_widget.dart';
import '../widgets/question_widget.dart';
import '../widgets/answer_result_dialog.dart';

class BibleGameScreen extends StatefulWidget {
  const BibleGameScreen({Key? key}) : super(key: key);

  @override
  State<BibleGameScreen> createState() => _BibleGameScreenState();
}

class _BibleGameScreenState extends State<BibleGameScreen> {
  String selectedLanguage = 'english';
  String selectedDifficulty = 'Easy';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('📖 Bible Game'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.leaderboard),
            onPressed: () => _showLeaderboard(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<BibleGameBloc>().add(ResetGameEvent()),
          ),
        ],
      ),
      body: BlocConsumer<BibleGameBloc, BibleGameState>(
        listener: (context, state) {
          if (state is BibleGameError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is BibleGameAnswerResult) {
            _showAnswerResult(context, state);
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Game Stats
                GameStatsWidget(
                  score: _getScore(state),
                  totalQuestions: _getTotalQuestions(state),
                  difficulty: _getDifficulty(state),
                ),
                
                const SizedBox(height: 20),
                
                if (state is BibleGameInitial) ...[
                  // Language Selection
                  LanguageSelectionWidget(
                    selectedLanguage: selectedLanguage,
                    onLanguageChanged: (language) {
                      setState(() => selectedLanguage = language);
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Difficulty Selection
                  DifficultySelectionWidget(
                    selectedDifficulty: selectedDifficulty,
                    onDifficultyChanged: (difficulty) {
                      setState(() => selectedDifficulty = difficulty);
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Start Game Button
                  ElevatedButton(
                    onPressed: () => _startGame(context),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: const TextStyle(fontSize: 18),
                    ),
                    child: const Text('🎮 Start Game'),
                  ),
                ] else if (state is BibleGameLoading) ...[
                  const Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading...'),
                      ],
                    ),
                  ),
                ] else if (state is BibleGameQuestion) ...[
                  // Question Display
                  QuestionWidget(
                    question: state.question,
                    onAnswerSelected: (answer) => _submitAnswer(context, answer),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // New Game Button
                  OutlinedButton(
                    onPressed: () => context.read<BibleGameBloc>().add(ResetGameEvent()),
                    child: const Text('🔄 New Game'),
                  ),
                ] else if (state is BibleGameLeaderboard) ...[
                  _buildLeaderboard(state),
                ],
                
                const SizedBox(height: 20),
                
                // Instructions
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'How to Play:',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text('1. Choose your preferred language (English or Malayalam)'),
                        const Text('2. Select difficulty level (Easy, Medium, or Hard)'),
                        const Text('3. Read the Bible verse and guess the correct reference'),
                        const Text('4. Try to get the highest score possible!'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _startGame(BuildContext context) {
    context.read<BibleGameBloc>().add(StartGameEvent(
      language: selectedLanguage,
      difficulty: selectedDifficulty,
    ));
  }

  void _submitAnswer(BuildContext context, String answer) {
    context.read<BibleGameBloc>().add(SubmitAnswerEvent(answer));
  }

  void _showAnswerResult(BuildContext context, BibleGameAnswerResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AnswerResultDialog(
        result: result,
        onContinue: () {
          Navigator.of(context).pop();
          if (result.nextQuestion != null) {
            // Continue with next question - this will be handled by the BLoC
          } else {
            // Game ended
            context.read<BibleGameBloc>().add(ResetGameEvent());
          }
        },
      ),
    );
  }

  void _showLeaderboard(BuildContext context) {
    context.read<BibleGameBloc>().add(const LoadLeaderboardEvent());
  }

  Widget _buildLeaderboard(BibleGameLeaderboard state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '🏅 Leaderboard',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => context.read<BibleGameBloc>().add(ResetGameEvent()),
                  child: const Text('Back to Game'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (state.entries.isEmpty)
              const Center(
                child: Text('No scores yet. Be the first to play!'),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.entries.length,
                itemBuilder: (context, index) {
                  final entry = state.entries[index];
                  final rank = index + 1;
                  String medal = '';
                  if (rank == 1) medal = '🥇';
                  else if (rank == 2) medal = '🥈';
                  else if (rank == 3) medal = '🥉';
                  
                  return ListTile(
                    leading: Text(
                      medal.isNotEmpty ? medal : '$rank.',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    title: Text(entry.userName),
                    subtitle: Text('${entry.difficulty} Level'),
                    trailing: Text(
                      '${entry.score}',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  int _getScore(BibleGameState state) {
    if (state is BibleGameQuestion) return state.score;
    if (state is BibleGameAnswerResult) return state.score;
    return 0;
  }

  int _getTotalQuestions(BibleGameState state) {
    if (state is BibleGameQuestion) return state.totalQuestions;
    if (state is BibleGameAnswerResult) return state.totalQuestions;
    return 0;
  }

  String _getDifficulty(BibleGameState state) {
    if (state is BibleGameQuestion) return state.difficulty;
    return selectedDifficulty;
  }
}
